import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { LAYOUTROUTING } from './layout.routing';
import { LayoutComponent } from '../layout/layout.component';
import { LandingComponent } from '../landing/landing.component';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { HttpLoaderFactory } from '../../app.module';
import { ChangePasswordComponent } from '../change-password/change-password.component';
import { AccountSettingsComponent } from '../account-settings/account-settings.component';
import { FormsModule } from '@angular/forms';
import { UsersComponent } from '../users/users.component';
import { UsersEditComponent } from '../users/edit/users-edit.component';
import { DataTablesModule } from 'angular-datatables';
import { EditorModule } from '@tinymce/tinymce-angular';
import { FileUploadModule } from 'ng2-file-upload';
import { MyDatePickerModule } from 'mydatepicker';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ImageCropperModule } from 'ngx-image-cropper';
import { FileCropperComponent } from '../../shared/file-cropper/file-cropper.component';
import { MaterialAppModule } from 'src/app/material.module';
import {NgSelectModule} from '@ng-select/ng-select';
import { ImagePopUPModel } from '../../shared/image-popup-model/image-popup-model';

	import { FarmComponent } from '../farm/farm.component';
	import { FarmEditComponent } from '../farm/edit/farm-edit.component';
	import { FarmDetailComponent } from '../farm/detail/farm-detail.component';
	import { LanguageComponent } from '../language/language.component';
	import { LanguageEditComponent } from '../language/edit/language-edit.component';
	import { LanguageDetailComponent } from '../language/detail/language-detail.component';
	import { CategoryComponent } from '../category/category.component';
	import { CategoryEditComponent } from '../category/edit/category-edit.component';
	import { CategoryDetailComponent } from '../category/detail/category-detail.component';
	import { SubCategoryComponent } from '../subcategory/subcategory.component';
	import { SubCategoryEditComponent } from '../subcategory/edit/subcategory-edit.component';
	import { SubCategoryDetailComponent } from '../subcategory/detail/subcategory-detail.component';
	import { UserFarmComponent } from '../userfarm/userfarm.component';
	import { UserFarmEditComponent } from '../userfarm/edit/userfarm-edit.component';
	import { UserFarmDetailComponent } from '../userfarm/detail/userfarm-detail.component';
	import { TrainingComponent } from '../training/training.component';
	import { TrainingEditComponent } from '../training/edit/training-edit.component';
	import { TrainingDetailComponent } from '../training/detail/training-detail.component';
	import { UserAssignTrainingComponent } from '../userassigntraining/userassigntraining.component';
	import { UserAssignTrainingEditComponent } from '../userassigntraining/edit/userassigntraining-edit.component';
	import { UserAssignTrainingDetailComponent } from '../userassigntraining/detail/userassigntraining-detail.component';
	import { FarmAssignTrainingComponent } from '../farmassigntraining/farmassigntraining.component';
	import { FarmAssignTrainingEditComponent } from '../farmassigntraining/edit/farmassigntraining-edit.component';
	import { FarmAssignTrainingDetailComponent } from '../farmassigntraining/detail/farmassigntraining-detail.component';
	import { MomentComponent } from '../moment/moment.component';
	import { MomentEditComponent } from '../moment/edit/moment-edit.component';
	import { MomentDetailComponent } from '../moment/detail/moment-detail.component';
	import { UserTrainingStatusComponent } from '../usertrainingstatus/usertrainingstatus.component';
	import { UserTrainingStatusEditComponent } from '../usertrainingstatus/edit/usertrainingstatus-edit.component';
	import { UserTrainingStatusDetailComponent } from '../usertrainingstatus/detail/usertrainingstatus-detail.component';


@NgModule({
  declarations: [
    LandingComponent,
    LayoutComponent,
    ChangePasswordComponent,
    AccountSettingsComponent,
    UsersComponent,
    UsersEditComponent,
    ImagePopUPModel,
    FileCropperComponent,
		FarmComponent,
		FarmEditComponent,
		FarmDetailComponent,
		LanguageComponent,
		LanguageEditComponent,
		LanguageDetailComponent,
		CategoryComponent,
		CategoryEditComponent,
		CategoryDetailComponent,
		SubCategoryComponent,
		SubCategoryEditComponent,
		SubCategoryDetailComponent,
		UserFarmComponent,
		UserFarmEditComponent,
		UserFarmDetailComponent,
		TrainingComponent,
		TrainingEditComponent,
		TrainingDetailComponent,
		UserAssignTrainingComponent,
		UserAssignTrainingEditComponent,
		UserAssignTrainingDetailComponent,
		FarmAssignTrainingComponent,
		FarmAssignTrainingEditComponent,
		FarmAssignTrainingDetailComponent,
		MomentComponent,
		MomentEditComponent,
		MomentDetailComponent,
		UserTrainingStatusComponent,
		UserTrainingStatusEditComponent,
		UserTrainingStatusDetailComponent
	
  ],
  imports: [
    CommonModule,
    FormsModule,
    EditorModule,
    FileUploadModule,
    MyDatePickerModule,
    ImageCropperModule,
    MaterialAppModule,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
    NgMultiSelectDropDownModule.forRoot(),
    RouterModule.forChild(LAYOUTROUTING),
    DataTablesModule,
    NgSelectModule
  ]
})
export class LayoutModule {
}
