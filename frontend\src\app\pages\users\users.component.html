<div class="breadcrumb-container" *ngIf="!isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">User Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'USERS.objNames' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" [routerLink]="['/dashboard/user/edit/0']"
                title="{{'COMMON.ADD' | translate}}">
            <span class="hidden-xs">{{'USERS.ADD_NEW_USERS' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card">
        <div class="table-responsive" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
                <thead>
                <tr>
                    <th>{{"USERS.Name" | translate}}</th>
                    <th>{{"USERS.Email" | translate}}</th>
                    <th>{{"USERS.Role" | translate}}</th>
                    <th>{{"USERS.Active" | translate}}</th>
                    <th>{{"USERS.Action" | translate}}</th>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let record of records;">
                    <td class="text-capitalize">{{record.fullName}}</td>
                    <td class="text-lowercase">{{record.email}}</td>
                    <td class="text-uppercase">{{record.commonRoleName}}</td>
                    <td>
                        <span class="label label-info">{{record.isActive ? 'Yes' : 'No'}}</span>
                    </td>
                    <td class="text-center">
                        <a title="Select" class="action-button" (click)="onItemSelection(record)"
                           *ngIf="isPlusButton">
                            {{'COMMON.SELECT' | translate}}
                        </a>
                        <a title="Edit" class="action-button"
                           [routerLink]="['/dashboard/user/edit/'+record.id]" *ngIf="!isPlusButton">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                        </a>
                        <a title="Delete" class="action-button" (click)="remove(record.id)"
                           *ngIf="!isPlusButton">
                            <i class="fa fa-trash" aria-hidden="true"></i>
                        </a>
                    </td>
                </tr>
                <tr *ngIf="records.length===0">
                    <td class="text-center" colspan="5">
                        {{"COMMON.NORECORDS" | translate}}
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="card data-body" *ngIf="hasDataLoad">

</div>
