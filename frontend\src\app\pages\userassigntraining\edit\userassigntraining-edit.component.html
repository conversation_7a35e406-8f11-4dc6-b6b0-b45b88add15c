<div class="breadcrumb-container" *ngIf="!isPlusButton">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">UserAssignTraining Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a [routerLink]="['/dashboard/user-assign-training']">{{'UserAssignTraining.objNames' | translate}}</a></li>
                <li class="active"
                    *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'UserAssignTraining.objName' | translate}}</li>
                <li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{userAssignTraining.name}}</li>
            </ol>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
    <div class="site-card">
    	 <form #userassigntrainingForm="ngForm" novalidate="novalidate">
            <div class="row justify-content-start">
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserAssignTraining.userId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="users"
			                                       bindLabel="email"
			                                       bindValue="id"
			                                       name="userAssignTrainingUserId"
			                                       #userAssignTrainingUserId="ngModel"
					                            		[(ngModel)]="userAssignTraining.userId" required="required" #UserId="ngModel"
			                                       [ngClass]="{'invalid-field':userAssignTrainingUserId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} users">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('userAssignTrainingUserIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserAssignTraining.trainingId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="trainings"
			                                       bindLabel="videoTitle"
			                                       bindValue="id"
			                                       name="userAssignTrainingTrainingId"
			                                       #userAssignTrainingTrainingId="ngModel"
					                            		[(ngModel)]="userAssignTraining.trainingId" required="required" #TrainingId="ngModel"
			                                       [ngClass]="{'invalid-field':userAssignTrainingTrainingId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} training">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('userAssignTrainingTrainingIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserAssignTraining.status" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="100"  name="userAssignTrainingStatus"  [(ngModel)]="userAssignTraining.status" #Status="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserAssignTraining.userVideoUrl" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="MAX"  name="userAssignTrainingUserVideoUrl"  [(ngModel)]="userAssignTraining.userVideoUrl" #UserVideoUrl="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserAssignTraining.assignedDate" | translate}}
		                        </label>
									<my-date-picker name="userAssignTrainingAssignedDate" [options]="myDatePickerOptions" [(ngModel)]="userAssignTraining.assignedDateCalendar"
										>
									</my-date-picker>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"UserAssignTraining.farmId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="farms"
			                                       bindLabel="name"
			                                       bindValue="id"
			                                       name="userAssignTrainingFarmId"
			                                       #userAssignTrainingFarmId="ngModel"
					                            		[(ngModel)]="userAssignTraining.farmId" required="required" #FarmId="ngModel"
			                                       [ngClass]="{'invalid-field':userAssignTrainingFarmId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} farm">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('userAssignTrainingFarmIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            </div>
        </form>
        <div class="clearfix"></div>
        <div class="col-md-12 no-padding text-right">
           <button title="Save" class="btn btn-primary site-button" type="button" (click)="save(userassigntrainingForm.form)"
	        *ngIf="authService.isAccessible('USERASSIGNTRAINING','AddButton')" 
	        [disabled]="authService.isDisabled('USERASSIGNTRAINING','AddButton')">
	            {{"COMMON.SAVE" | translate}}
	        </button>
	        <button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button" (click)="navigate()">
	            {{"COMMON.CANCEL" | translate}}
	        </button>
            <div class="clearfix"></div>
        </div>
        <div class="clearfix"></div>
    </div>
    <div class="clearfix"></div>
</div>
		<div class="modal fade nav-scroll" id="userAssignTrainingUserIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-users [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-users>
		            </div>
		        </div>
		    </div>
		</div>
		<div class="modal fade nav-scroll" id="userAssignTrainingTrainingIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-training [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-training>
		            </div>
		        </div>
		    </div>
		</div>
		<div class="modal fade nav-scroll" id="userAssignTrainingFarmIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-farm [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-farm>
		            </div>
		        </div>
		    </div>
		</div>

