import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FilterParam } from 'src/app/models/filterparam';
import { Users } from 'src/app/models/users';
import { UsersService } from 'src/app/services/users.service';
import { LoadingService } from '../../../services/loading.service';
import { RestResponse } from '../../../shared/auth.model';
import { CommonUtil } from '../../../shared/common.util';
import { ToastService } from '../../../shared/toast.service';

@Component({
    selector: 'app-users-edit',
    templateUrl: './users-edit.component.html',
    styleUrls: ['./users-edit.component.scss']
})
export class UsersEditComponent implements OnInit {

    user: Users;
    onClickValidation: boolean;
    roles: any[];
    request: any;
    filterParam: FilterParam;

    constructor(private route: ActivatedRoute, private usersService: UsersService, private toastService: ToastService,
        private loadingService: LoadingService, private router: Router, public commonUtil: CommonUtil) {
        this.user = new Users();
        this.user.roleName = '';
        this.request = {} as any;
        this.request.recordId = this.route.snapshot.paramMap.get('id');
        this.request.isNewRecord = true;
        this.request.onClickValidation = false;
        this.filterParam = new FilterParam();
    }

    async ngOnInit() {
        this.loadingService.show();
        await this.fetchRoles();
        if (this.request.recordId <= 0) {
            this.loadingService.hide();
            return;
        }
        await this.fetchUserDetail();
        this.loadingService.hide();
    }

    async fetchUserDetail() {
        try {
            const response: RestResponse = await this.usersService.fetch(this.request.recordId).toPromise();
            if (!response.status) {
                this.toastService.error(response.message);
                return;
            }
            this.request.isNewRecord = false;
            this.user = response.data;
        } catch (e) {
            this.toastService.error(e.message);
        }
    }

    async fetchRoles() {
        try {
            const response: RestResponse = await this.usersService.GetAllRoles(this.filterParam);
            if (!response.status) {
                this.toastService.error(response.message);
                return;
            }
            this.roles = response.data;
        } catch (e) {
            this.toastService.error(e.message);
        }
    }

    async save(valid) {
        if (!valid) {
            this.request.onClickValidation = true;
            return;
        }
        this.loadingService.show();
        try {
            this.request.isRequested = true;
            const method = this.request.isNewRecord ? 'save' : 'update';
            this.user.roles = null;
            const response: RestResponse = await this.usersService[method](this.user);
            this.loadingService.hide();
            if (!response.status) {
                this.toastService.error(response.message);
                this.request.isRequested = false;
                return;
            }
            this.toastService.success(response.message);
            await this.router.navigateByUrl('/dashboard/users');
        } catch (e) {
            this.loadingService.hide();
            this.request.isRequested = false;
            this.toastService.error(e.message);
        }
    }
}
