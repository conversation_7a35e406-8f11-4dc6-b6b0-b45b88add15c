$font-color:#707070;
$font-color-2:#B41D86;
$btn-bg-color: transparent linear-gradient(294deg, #99078D 0%, #FA5676 100%) 0% 0% no-repeat padding-box;
$text-center:center;
$text-right:right;
$text-upper-case:uppercase;

.create-password{
background: transparent linear-gradient(148deg, #ED4C78 0%, #9013BE 100%) 0% 0% no-repeat padding-box;
  background-size: 100%;
  background-repeat: no-repeat;
  overflow: hidden;
  height: 100vh;
  z-index:0;
  .bg-color{
    background-color:#fff;
    height: 100vh;
    position:relative;
    z-index: 1;
  }

  @extend .inner-widget;
  .logo{
    width:152px;
    min-width:150px;
    margin:0 auto;
    padding-top:80px;
    img{
      width:100%;
    }
  }
  .form-group{
    width:273px;
        min-width:273px;
        margin:0 auto;
    label{
    }
    .input{
      border-style: none;
      outline: none;
      background:transparent;
      box-shadow: none;
      border-bottom:1px solid #********;
      border-radius: 0px;
      position: relative;
      width:100%;

    }
    .input-border{
        border-bottom: 3px solid #********;
        color:#000;
    }

    .input-icon{
      position: absolute;
      top: 47px;
      right: 0;
      font-size: 16px;
    }

  }
  .inner-widget
  {
      position: relative;
      .img{
          display: flex;
          flex-direction: row;
          align-content: center;
          align-items: center;
          width:100%;
          height: 100vh;
          img{
              width:50%;
              margin:0 auto;
          }
      }
  .account-issue-option{
      width:273px;
        min-width:273px;
        margin:0 auto;
      text-align: $text-right;
      h5{
        color:$font-color;
      }
    }
    .no-account-option{
      text-align: $text-center;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      h5{
        color:$font-color;
        text-transform: $text-upper-case;
        font-size: 14px;
        padding-right: 2px;
      }
      .link{
        color:$font-color-2;
        text-transform: $text-upper-case;
        font-size: 14px;
        padding-left: 2px;
      }
    }
    .bottom-line{
      width:273px;
        min-width:273px;
        margin:0 auto;
        padding-top:175px;
      p{
        font-size: 12px;
        color:$font-color;
        text-align: $text-center;
      }
    }

    .display-align{
      display: flex;
      align-items: center;
      justify-content: center;
      .img{
        width:20px;
        width:20px;

      }
    }

    .banner-footer{
        position: absolute;
        bottom:-74px;
        right:0;
        width:50%;
        img{

        }
    }

  }
}





.inner-widget{
  background-color:transparent;

  .page-heading-title{
    text-align: $text-center;
  }
  .form-group{
    padding:15px 0px;
    position: relative;
    label{
      color:$font-color;
      font-size:14px;
      font-weight: 400;

    }
  }
  .btn-group{
    width:273px;
      min-width:273px;
      margin:0 auto;
      display: block;
    .btn{
      width:273px;
      min-width:273px;
      background:$btn-bg-color;
      border-style: none;
      outline: none;
      padding:9px 0px;
      font-size: 16px;
      margin:0 auto;
    }
  }
}
