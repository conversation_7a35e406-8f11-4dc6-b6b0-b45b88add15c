import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { Category } from '../../../models/category';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import {CommonUtil} from '../../../shared/common.util';
import { CategoryManager } from '../category.manager';

import { LanguageManager } from '../../language/language.manager';
import { Language } from '../../../models/language';
import { CommonEventService } from '../../../shared/common.event.service';
import { Constant } from '../../../config/constants';
declare const $: any;

@Component({
  selector: 'app-category-edit',
  templateUrl: './category-edit.component.html',
  styleUrls: ['./category-edit.component.scss']
})

export class CategoryEditComponent extends BaseEditComponent implements OnInit {
  public category: Category;
	public languages:Language[];
	public groupCodeOptions: Array<any>;
  
  constructor(protected route: ActivatedRoute, protected categoryManager: CategoryManager, 
  			  protected toastService: ToastService,protected loadingService: LoadingService, protected router: Router, 
  			  protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService 
  			  , private languageManager:LanguageManager
  			  ,public commonUtil:CommonUtil ) {
    	super(categoryManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
  	this.category = new Category();
  	this.category.isActive=true;   
    this.setRecord(this.category);
	this.groupCodeOptions = Constant.CATEGORY_GROUPCODE_OPTIONS;
	
     
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
  	this.languages = new Array<Language>();
    this.init();
  }

  onFetchCompleted() {
  	this.category = Category.fromResponse(this.record);
    this.setRecord(this.category);
  }

  
  
  async fetchAssociatedData() {
	this.languages = await this.languageManager.fetchAllData(null);       		
    this.afterFetchAssociatedCompleted();
  }
  
  afterFetchAssociatedCompleted() {
    	const languageIdId: string = this.route.snapshot.queryParamMap.get('Language');
		if (languageIdId){
			this.onAssociatedValueSelected({"id":languageIdId},'categoryLanguageIdSelect');
		}
	}
  
  onSaveSuccess(data: any) {
  	this.navigate('/dashboard/category');
  }	
	  
	
	checkConditionToReload(records: BaseModel[], selectedRecord: any){
		if (!records.some(x => x.id === selectedRecord.id)) {
			this.fetchAssociatedData();
		}
	}
	
	onAssociatedValueSelected(selectedRecord: any, selectedField: any) {	
	if(this.request.popupId){
		$('#'+this.request.popupId).appendTo('body').modal('hide');
	}
		if((!this.isNullOrUndefined(selectedField) && selectedField==='categoryLanguageIdSelect') || this.request.popupId==='categoryLanguageIdPopup'){
			this.category.languageId = selectedRecord.id;
			this.checkConditionToReload(this.languages, selectedRecord);
			return;
	    }
  	
	 }
}
