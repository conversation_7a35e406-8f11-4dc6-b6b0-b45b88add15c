import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Farm } from './farm';
import { UserAssignTraining } from './userassigntraining';
export class Moment extends BaseModel {
	
				tenantId: number;
			  	slug: string;
			  	farmIdDetail: Farm;
			  	farmId: string;
			  	type: string;
			  	description: string;
			  	mediaType: string;
			  	mediaUrl: string;
			  	assignTrainingIdDetail: UserAssignTraining;
			  	assignTrainingId: string;
			  	userVideo: string;
			  	instruction: string;
			  	completedBy: number;
			  	status: string;
	
    constructor() {
        super();
			this.isDeleted=false;
			this.isActive=true;
    }
    
   static fromResponse(data: any) : Moment {
		const obj = new Moment();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
	  	obj.farmIdDetail = data.farmIdDetail;
	  	obj.farmId = data.farmId;
		obj.type = data.type;
		obj.description = data.description;
		obj.mediaType = data.mediaType;
		obj.mediaUrl = data.mediaUrl;
	  	obj.assignTrainingIdDetail = data.assignTrainingIdDetail;
	  	obj.assignTrainingId = data.assignTrainingId;
		obj.userVideo = data.userVideo;
		obj.instruction = data.instruction;
		obj.completedBy = data.completedBy;
		obj.status = data.status;
		return obj;
	}

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		if (this.isNullOrUndefinedAndEmpty(this.type)) {
            form.controls.type.setErrors({ invalid: true });
            return false;
        }
        return true;
    }

    forRequest() {
				this.type = this.trimMe(this.type);
				this.description = this.trimMe(this.description);
				this.mediaType = this.trimMe(this.mediaType);
				this.mediaUrl = this.trimMe(this.mediaUrl);
				this.userVideo = this.trimMe(this.userVideo);
				this.instruction = this.trimMe(this.instruction);
				this.status = this.trimMe(this.status);
        return this;
    }
}
