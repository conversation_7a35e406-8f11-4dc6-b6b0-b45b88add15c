import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';
import { UsersService } from 'src/app/services/users.service';
import { FilterParam } from 'src/app/models/filterparam';
import { RestResponse } from 'src/app/shared/auth.model';

@Injectable({
    providedIn: 'root'
})
export class UsersManager extends BaseManager {

    constructor(private usersService: UsersService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(usersService, loadingService, toastService);
    }
    fetchAllUsers(filterParam: FilterParam): Promise<RestResponse> {
        const promise = new Promise<RestResponse>(async (resolve, reject) => {
            try {
                const response: RestResponse = await this.usersService.fetchAllUsers(filterParam);

                if (!response.status) {
                    resolve(response);
                    return;
                }
                response.data = this.onFetchAllSuccess(response.data);
                resolve(response);
            } catch (error) {
                this.onFailure(error);
                reject(error);
            }
        });
        return promise;
    }
}
