import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { MomentService } from './moment.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class MomentManager extends BaseManager {

    constructor(private momentService: MomentService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(momentService, loadingService, toastService);
    }
}
