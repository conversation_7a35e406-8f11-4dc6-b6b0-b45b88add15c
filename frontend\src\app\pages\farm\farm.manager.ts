import { Injectable } from '@angular/core';
import { BaseManager } from '../../config/base.manager';
import { FarmService } from './farm.service';
import { LoadingService } from 'src/app/services/loading.service';
import { ToastService } from 'src/app/shared/toast.service';

@Injectable({
    providedIn: 'root'
})
export class FarmManager extends BaseManager {

    constructor(private farmService: FarmService, protected loadingService: LoadingService, protected toastService: ToastService) {
        super(farmService, loadingService, toastService);
    }
}
