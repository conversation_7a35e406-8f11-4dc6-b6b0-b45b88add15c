<div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">SubCategory Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'SubCategory.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('SUBCATEGORY','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('SUBCATEGORY','AddButton')">
            <span class="hidden-xs">{{'SubCategory.ADD_NEW_SUBCATEGORY' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Category.objName' | translate}} {{'Category.title' | translate}}</th>
					    		<th>{{'SubCategory.title' | translate}}</th>
					    		<th>{{'SubCategory.description' | translate}}</th>
					    		<th>{{'Language.objName' | translate}} {{'Language.name' | translate}}</th>
					    		<th>{{'SubCategory.commonTitle' | translate}}</th>
					    		<th>{{'SubCategory.groupCode' | translate}}</th>
					    		<th>{{'SubCategory.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        		<td>
				                    		<a *ngIf="record.categoryIdDetail" class="primary-color" [routerLink]="['/dashboard/category/detail/'+record.categoryIdDetail.id]">
				                       		{{record.categoryIdDetail.title}}
						        		
						        		</a> 
					        		</td>
					        			<td>{{record.title}}</td>
					        		<td>
					        			<div [innerHtml]="record.description" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        		<td>
				                    		<a *ngIf="record.languageIdDetail" class="primary-color" [routerLink]="['/dashboard/language/detail/'+record.languageIdDetail.id]">
				                       		{{record.languageIdDetail.name}}
						        		
						        		</a> 
					        		</td>
					        			<td>{{record.commonTitle}}</td>
					        			<td>{{record.groupCode}}</td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">	         
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('SUBCATEGORY','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('SUBCATEGORY','DetailButton')"
				              (click)="loadDetailPage(record.id)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('SUBCATEGORY','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('SUBCATEGORY','EditButton')"
                           [routerLink]="['/dashboard/sub-category/edit/'+record.id]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('SUBCATEGORY','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('SUBCATEGORY','DeleteButton')"
	                           (click)="remove(record.id)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>		
            </table>
    </div>
</div>

<app-subcategory-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-subcategory-edit>

<div class="modal fade site-detail-modal right" id="subCategoryDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="subCategoryDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-subcategory-detail [recordId]="selectedId"></app-subcategory-detail>
            </div>
        </div>
    </div>
</div>

