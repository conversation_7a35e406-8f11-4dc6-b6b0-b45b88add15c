<div class="breadcrumb-container" *ngIf="!isPlusButton">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">SubCategory Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a [routerLink]="['/dashboard/sub-category']">{{'SubCategory.objNames' | translate}}</a></li>
                <li class="active"
                    *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'SubCategory.objName' | translate}}</li>
                <li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{subCategory.name}}</li>
            </ol>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
    <div class="site-card">
    	 <form #subcategoryForm="ngForm" novalidate="novalidate">
            <div class="row justify-content-start">
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"SubCategory.categoryId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="categories"
			                                       bindLabel="title"
			                                       bindValue="id"
			                                       name="subCategoryCategoryId"
			                                       #subCategoryCategoryId="ngModel"
					                            		[(ngModel)]="subCategory.categoryId" required="required" #CategoryId="ngModel"
			                                       [ngClass]="{'invalid-field':subCategoryCategoryId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} category">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('subCategoryCategoryIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"SubCategory.title" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="255"  name="subCategoryTitle" required="required" [(ngModel)]="subCategory.title" #Title="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"SubCategory.description" | translate}}
		                        </label>
				                    	<textarea class="form-control" rows="5" name="subCategoryDescription"  [(ngModel)]="subCategory.description" #Description="ngModel"></textarea>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"SubCategory.languageId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="languages"
			                                       bindLabel="name"
			                                       bindValue="id"
			                                       name="subCategoryLanguageId"
			                                       #subCategoryLanguageId="ngModel"
					                            		[(ngModel)]="subCategory.languageId" required="required" #LanguageId="ngModel"
			                                       [ngClass]="{'invalid-field':subCategoryLanguageId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} language">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('subCategoryLanguageIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"SubCategory.commonTitle" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="255"  name="subCategoryCommonTitle"  [(ngModel)]="subCategory.commonTitle" #CommonTitle="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"SubCategory.groupCode" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="255"  name="subCategoryGroupCode"  [(ngModel)]="subCategory.groupCode" #GroupCode="ngModel"> 
									</div>										 
							</div>
						</div>
            </div>
        </form>
        <div class="clearfix"></div>
        <div class="col-md-12 no-padding text-right">
           <button title="Save" class="btn btn-primary site-button" type="button" (click)="save(subcategoryForm.form)"
	        *ngIf="authService.isAccessible('SUBCATEGORY','AddButton')" 
	        [disabled]="authService.isDisabled('SUBCATEGORY','AddButton')">
	            {{"COMMON.SAVE" | translate}}
	        </button>
	        <button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button" (click)="navigate()">
	            {{"COMMON.CANCEL" | translate}}
	        </button>
            <div class="clearfix"></div>
        </div>
        <div class="clearfix"></div>
    </div>
    <div class="clearfix"></div>
</div>
		<div class="modal fade nav-scroll" id="subCategoryCategoryIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-category [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-category>
		            </div>
		        </div>
		    </div>
		</div>
		<div class="modal fade nav-scroll" id="subCategoryLanguageIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-language [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-language>
		            </div>
		        </div>
		    </div>
		</div>

