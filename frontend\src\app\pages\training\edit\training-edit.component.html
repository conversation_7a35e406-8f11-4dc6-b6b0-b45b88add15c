<div class="breadcrumb-container" *ngIf="!isPlusButton">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Training Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a [routerLink]="['/dashboard/training']">{{'Training.objNames' | translate}}</a></li>
                <li class="active"
                    *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'Training.objName' | translate}}</li>
                <li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{training.name}}</li>
            </ol>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
    <div class="site-card">
    	 <form #trainingForm="ngForm" novalidate="novalidate">
            <div class="row justify-content-start">
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Training.categoryId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="categories"
			                                       bindLabel="title"
			                                       bindValue="id"
			                                       name="trainingCategoryId"
			                                       #trainingCategoryId="ngModel"
					                            		[(ngModel)]="training.categoryId" required="required" #CategoryId="ngModel"
			                                       [ngClass]="{'invalid-field':trainingCategoryId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} category">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('trainingCategoryIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Training.subCategoryId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="subCategories"
			                                       bindLabel="title"
			                                       bindValue="id"
			                                       name="trainingSubCategoryId"
			                                       #trainingSubCategoryId="ngModel"
					                            		[(ngModel)]="training.subCategoryId" required="required" #SubCategoryId="ngModel"
			                                       [ngClass]="{'invalid-field':trainingSubCategoryId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} subCategory">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('trainingSubCategoryIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Training.languageId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="languages"
			                                       bindLabel="name"
			                                       bindValue="id"
			                                       name="trainingLanguageId"
			                                       #trainingLanguageId="ngModel"
					                            		[(ngModel)]="training.languageId" required="required" #LanguageId="ngModel"
			                                       [ngClass]="{'invalid-field':trainingLanguageId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} language">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('trainingLanguageIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Training.videoTitle" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="255"  name="trainingVideoTitle"  [(ngModel)]="training.videoTitle" #VideoTitle="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Training.description" | translate}}
		                        </label>
				                    	<textarea class="form-control" rows="5" name="trainingDescription"  [(ngModel)]="training.description" #Description="ngModel"></textarea>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Training.videoUrl" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="MAX"  name="trainingVideoUrl"  [(ngModel)]="training.videoUrl" #VideoUrl="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Training.publishedForTrainingFeed" | translate}}
		                        </label>
									<div class="material-switch">
										<input id="trainingPublishedForTrainingFeedId" name="trainingPublishedForTrainingFeed" type="checkbox"  [(ngModel)]="training.publishedForTrainingFeed" />
										<label for="trainingPublishedForTrainingFeedId" class="label-primary"></label>
									</div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Training.commonVideoTitle" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="MAX"  name="trainingCommonVideoTitle"  [(ngModel)]="training.commonVideoTitle" #CommonVideoTitle="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Training.groupCode" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="255"  name="trainingGroupCode"  [(ngModel)]="training.groupCode" #GroupCode="ngModel"> 
									</div>										 
							</div>
						</div>
            </div>
        </form>
        <div class="clearfix"></div>
        <div class="col-md-12 no-padding text-right">
           <button title="Save" class="btn btn-primary site-button" type="button" (click)="save(trainingForm.form)"
	        *ngIf="authService.isAccessible('TRAINING','AddButton')" 
	        [disabled]="authService.isDisabled('TRAINING','AddButton')">
	            {{"COMMON.SAVE" | translate}}
	        </button>
	        <button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button" (click)="navigate()">
	            {{"COMMON.CANCEL" | translate}}
	        </button>
            <div class="clearfix"></div>
        </div>
        <div class="clearfix"></div>
    </div>
    <div class="clearfix"></div>
</div>
		<div class="modal fade nav-scroll" id="trainingCategoryIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-category [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-category>
		            </div>
		        </div>
		    </div>
		</div>
		<div class="modal fade nav-scroll" id="trainingSubCategoryIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-subcategory [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-subcategory>
		            </div>
		        </div>
		    </div>
		</div>
		<div class="modal fade nav-scroll" id="trainingLanguageIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-language [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-language>
		            </div>
		        </div>
		    </div>
		</div>

