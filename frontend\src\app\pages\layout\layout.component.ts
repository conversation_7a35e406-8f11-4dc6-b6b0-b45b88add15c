import {Component, OnDestroy, OnInit} from '@angular/core';
import {AuthService} from '../../shared/auth.services';
import {CommonUtil} from '../../shared/common.util';

declare const $: any;

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss']
})
export class LayoutComponent implements OnInit, OnDestroy {
  
	  isShowSubMenu: boolean[];

	  constructor(public commonUtil: CommonUtil, public authService: AuthService) {
	    this.isShowSubMenu = new Array<boolean>();
	  }
	
	  ngOnInit() {
	    $('#content').addClass('window-content-body');
	    $('#content').removeClass('mobile-content-body');
	    $('#main-container').removeClass('base-page-container');
	    $('#main-container').addClass('landing-page-container');
	  }
	
	  ngOnDestroy() {
	    $('#main-container').addClass('base-page-container');
	    $('#main-container').removeClass('landing-page-container');
	  }
	  
	  showSubMenu(type) {
		this.isShowSubMenu[type] = !this.isShowSubMenu[type];
	  }
}
