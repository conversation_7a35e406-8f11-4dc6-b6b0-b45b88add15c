<div class="breadcrumb-container">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Users Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li [routerLink]="['/dashboard/users']">{{"USERS.objNames" | translate}}</li>
                <li class="active"
                    *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'USERS.objName' | translate}}</li>
                <li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{user.fullName}}</li>
            </ol>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
    <div class="site-card">
        <form #recordForm="ngForm" novalidate="novalidate">
            <div class="col-md-6">
                <div class="form-group margin-bottom-10"
                     [ngClass]="{'has-error':!firstName.valid && request.onClickValidation}">
                    <label>{{"USERS.FirstName" | translate}}</label>
                    <input class="form-control" type="text" name="firstName" #firstName="ngModel"
                           [(ngModel)]="user.firstName"
                           required="required" placeholder="First Name">
                </div>
                <div class="form-group margin-bottom-10"
                     [ngClass]="{'has-error':!lastname.valid && request.onClickValidation}">
                    <label>{{"USERS.LastName" | translate}}</label>
                    <input class="form-control" type="text" name="lastname" #lastname="ngModel"
                           [(ngModel)]="user.lastName"
                           required="required" placeholder="Last Name">
                </div>
                <div class="form-group margin-bottom-10"
                     [ngClass]="{'has-error':!role.valid && request.onClickValidation}">
                    <label>{{"USERS.Role" | translate}}</label>
                    <ng-select [items]="roles"
                               bindLabel="commonName"
                               bindValue="name"
                               name="role"
                               [(ngModel)]="user.roleName"
                               [ngClass]="{'invalid-field':!role.valid && onClickValidation}"
                               required="required"
                               [disabled]="!request.isNewRecord" #role="ngModel" placeholder="Please Select Role">
                    </ng-select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group margin-bottom-10"
                     [ngClass]="{'has-error':!email.valid && request.onClickValidation}">
                    <label>{{"USERS.Email" | translate}}</label>
                    <input class="form-control" type="email" name="email" #email="ngModel" [(ngModel)]="user.email"
                           required="required" placeholder="Email">
                </div>
                <div class="form-group margin-bottom-10"
                     [ngClass]="{'has-error':!phone.valid && request.onClickValidation}">
                    <label>{{"USERS.PhoneNumber" | translate}}</label>
                    <input class="form-control" type="text" name="phone" #phone="ngModel" [(ngModel)]="user.phoneNumber"
                           required="required" placeholder="Phone Number" pattern="[0-9]*"
                           minlength="10"
                           maxlength="10">
                </div>

            </div>
            <div class="clearfix"></div>
            <div class="col-md-12 no-padding text-right">
                <button class="btn btn-default site-button margin-right-10" type="button"
                        (click)="save(recordForm.form.valid)"
                        [disabled]="request.isRequested">
                    <i class="lnr lnr-checkmark-circle"></i> {{"COMMON.SAVE" | translate}}
                </button>
                <button class="btn btn-primary site-cancel-button " type="button"
                        [routerLink]="['/dashboard/users']">
                    <i class="lnr lnr-cross-circle "></i>
                    {{"COMMON.CANCEL" | translate}}
                </button>
                <div class="clearfix"></div>
            </div>
        </form>
        <div class="clearfix"></div>
    </div>
</div>
