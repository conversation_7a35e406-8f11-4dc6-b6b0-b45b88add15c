export const Constant = {
		CATEGORY_GROUPCODE_OPTIONS: [
	{
		id: '',
		name: 'Category.GROUPCODE_'
	}
	],
	MY_DATE_PICKER: {
		DATE_TYPE: 'dd/mm/yyyy'
	},
    VIEW_USER_MAPPING: {
    	COMMON_ACCESS:{    	
	        UserPage: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        AccountSettingPage: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        }     
        },        
        FARM_ACCESS:{
        	Page : {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
        	AddButton: {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },  
	        EditButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DeleteButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DetailButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        }
        },
        LANGUAGE_ACCESS:{
        	Page : {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
        	AddButton: {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },  
	        EditButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DeleteButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DetailButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        }
        },
        CATEGORY_ACCESS:{
        	Page : {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
        	AddButton: {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },  
	        EditButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DeleteButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DetailButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        }
        },
        SUBCATEGORY_ACCESS:{
        	Page : {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
        	AddButton: {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },  
	        EditButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DeleteButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DetailButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        }
        },
        USERFARM_ACCESS:{
        	Page : {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
        	AddButton: {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },  
	        EditButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DeleteButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DetailButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        }
        },
        TRAINING_ACCESS:{
        	Page : {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
        	AddButton: {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },  
	        EditButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DeleteButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DetailButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        }
        },
        USERASSIGNTRAINING_ACCESS:{
        	Page : {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
        	AddButton: {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },  
	        EditButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DeleteButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DetailButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        }
        },
        FARMASSIGNTRAINING_ACCESS:{
        	Page : {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
        	AddButton: {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },  
	        EditButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DeleteButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DetailButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        }
        },
        MOMENT_ACCESS:{
        	Page : {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
        	AddButton: {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },  
	        EditButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DeleteButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DetailButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        }
        },
        USERTRAININGSTATUS_ACCESS:{
        	Page : {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
        	AddButton: {
            	SHOW_TO_ROLE: ['ROLE_ADMIN'],
            	ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },  
	        EditButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DeleteButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        },
	        DetailButton: {
	            SHOW_TO_ROLE: ['ROLE_ADMIN'],
	            ENABLED_FOR_ROLE: ['ROLE_ADMIN'],
	        }
        },
    }
}

