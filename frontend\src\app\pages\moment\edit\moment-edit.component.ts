import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { Moment } from '../../../models/moment';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import {CommonUtil} from '../../../shared/common.util';
import { MomentManager } from '../moment.manager';

import { FarmManager } from '../../farm/farm.manager';
import { Farm } from '../../../models/farm';
import { UserAssignTrainingManager } from '../../userassigntraining/userassigntraining.manager';
import { UserAssignTraining } from '../../../models/userassigntraining';
import { CommonEventService } from '../../../shared/common.event.service';
import { MAT_DATE_FORMATS } from '@angular/material/core';

export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'DD/MM/YYYY',
  },
  display: {
    dateInput: 'DD/MM/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'DD/MM/YYYY',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};
declare const $: any;

@Component({
  selector: 'app-moment-edit',
  templateUrl: './moment-edit.component.html',
  styleUrls: ['./moment-edit.component.scss'],
   providers: [
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS }
  ]
})

export class MomentEditComponent extends BaseEditComponent implements OnInit {
  public moment: Moment;
	public farms:Farm[];
	public userAssignTrainings:UserAssignTraining[];
  
  constructor(protected route: ActivatedRoute, protected momentManager: MomentManager, 
  			  protected toastService: ToastService,protected loadingService: LoadingService, protected router: Router, 
  			  protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService 
  			  , private farmManager:FarmManager, private userassigntrainingManager:UserAssignTrainingManager
  			  ,public commonUtil:CommonUtil ) {
    	super(momentManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
  	this.moment = new Moment();
  	this.moment.isActive=true;   
    this.setRecord(this.moment);
	
     
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
  	this.farms = new Array<Farm>();
  	this.userAssignTrainings = new Array<UserAssignTraining>();
    this.init();
  }

  onFetchCompleted() {
  	this.moment = Moment.fromResponse(this.record);
    this.setRecord(this.moment);
  }

  
  
  async fetchAssociatedData() {
	this.farms = await this.farmManager.fetchAllData(null);       		
	this.userAssignTrainings = await this.userassigntrainingManager.fetchAllData(null);       		
    this.afterFetchAssociatedCompleted();
  }
  
  afterFetchAssociatedCompleted() {
    	const farmIdId: string = this.route.snapshot.queryParamMap.get('Farm');
		if (farmIdId){
			this.onAssociatedValueSelected({"id":farmIdId},'momentFarmIdSelect');
		}
    	const assignTrainingIdId: string = this.route.snapshot.queryParamMap.get('UserAssignTraining');
		if (assignTrainingIdId){
			this.onAssociatedValueSelected({"id":assignTrainingIdId},'momentAssignTrainingIdSelect');
		}
	}
  
  onSaveSuccess(data: any) {
  	this.navigate('/dashboard/moment');
  }	
	  
	
	checkConditionToReload(records: BaseModel[], selectedRecord: any){
		if (!records.some(x => x.id === selectedRecord.id)) {
			this.fetchAssociatedData();
		}
	}
	
	onAssociatedValueSelected(selectedRecord: any, selectedField: any) {	
	if(this.request.popupId){
		$('#'+this.request.popupId).appendTo('body').modal('hide');
	}
		if((!this.isNullOrUndefined(selectedField) && selectedField==='momentFarmIdSelect') || this.request.popupId==='momentFarmIdPopup'){
			this.moment.farmId = selectedRecord.id;
			this.checkConditionToReload(this.farms, selectedRecord);
			return;
	    }
		if((!this.isNullOrUndefined(selectedField) && selectedField==='momentAssignTrainingIdSelect') || this.request.popupId==='momentAssignTrainingIdPopup'){
			this.moment.assignTrainingId = selectedRecord.id;
			this.checkConditionToReload(this.userAssignTrainings, selectedRecord);
			return;
	    }
  	
	 }
}
