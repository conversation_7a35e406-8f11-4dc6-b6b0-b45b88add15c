{"name": "integrax", "version": "0.0.0", "scripts": {"ng": "ng", "start": "node --openssl-legacy-provider node_modules/@angular/cli/bin/ng serve", "build": "node --openssl-legacy-provider node_modules/@angular/cli/bin/ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^12.2.16", "@angular/cdk": "^10.2.7", "@angular/common": "^12.2.16", "@angular/compiler": "^12.2.16", "@angular/core": "^12.2.16", "@angular/forms": "^12.2.16", "@angular/localize": "^12.2.16", "@angular/material": "^10.2.7", "@angular/platform-browser": "^12.2.16", "@angular/platform-browser-dynamic": "^12.2.16", "@angular/router": "^12.2.16", "@ckeditor/ckeditor5-angular": "^4.0.0", "@ckeditor/ckeditor5-build-classic": "^34.2.0", "@ffmpeg/core": "^0.11.0", "@ffmpeg/ffmpeg": "^0.11.6", "@ng-select/ng-select": "^7.2.0", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "@popperjs/core": "^2.11.0", "@tinymce/tinymce-angular": "^4.2.4", "@types/node": "^12.20.55", "angular-2-local-storage": "^3.0.2", "angular-datatables": "^12.0.0", "aos": "^2.3.4", "apexcharts": "^3.35.0", "bootstrap": "^5.2.0", "bootstrap-icons": "^1.9.1", "core-js": "^2.5.4", "datatables.net": "^1.12.1", "datatables.net-dt": "^1.12.1", "datatables.net-responsive-dt": "^2.3.0", "exceljs": "^2.0.1", "file-saver": "^2.0.2", "howler": "^2.2.3", "jquery": "^3.6.0", "moment": "^2.29.4", "mydatepicker": "^9.0.2", "ng-apexcharts": "^1.7.1", "ng-multiselect-dropdown": "^0.2.4", "ng-recaptcha": "^7.0.1", "ng2-file-upload": "^1.3.0", "ng2-image-compress": "^7.0.7", "ng2-tel-input": "^2.3.0", "ngx-google-places-autocomplete": "^2.0.5", "ngx-image-compress": "^11.0.3", "ngx-image-cropper": "^1.4.1", "ngx-infinite-scroll": "^13.0.0", "ngx-mask": "^13.0.1", "ngx-spinner": "^13.1.1", "rxjs": "^6.5.4", "swiper": "^6.8.4", "time-ago-pipe": "^1.3.2", "tinymce": "^7.1.2", "tslib": "^2.4.0", "zone.js": "^0.11.6"}, "devDependencies": {"@angular-devkit/build-angular": "^12.2.17", "@angular/cli": "^12.2.17", "@angular/compiler-cli": "^12.2.16", "@angular/language-service": "^12.2.16", "@types/datatables.net": "^1.10.23", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/jquery": "^3.5.14", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "^6.3.20", "karma-chrome-launcher": "^3.1.1", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "^4.0.2", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~7.0.0", "tslint": "~6.1.0", "typescript": "~4.3.5"}}