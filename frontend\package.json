{"name": "redeemablevoucher", "version": "0.0.0", "scripts": {"ng": "ng", "start": "node --openssl-legacy-provider node_modules/@angular/cli/bin/ng serve", "build": "node --openssl-legacy-provider node_modules/@angular/cli/bin/ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "~12.2.13", "@angular/cdk": "^10.2.7", "@angular/common": "~12.2.13", "@angular/compiler": "~12.2.13", "@angular/core": "~12.2.13", "@angular/forms": "~12.2.13", "@angular/localize": "^12.2.13", "@angular/material": "^10.2.7", "@angular/platform-browser": "~12.2.13", "@angular/platform-browser-dynamic": "~12.2.13", "@angular/router": "~12.2.13", "@ng-select/ng-select": "^7.2.0", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "@tinymce/tinymce-angular": "^4.2.4", "@types/node": "^12.11.1", "angular-2-local-storage": "^3.0.2", "angular-datatables": "^12.0.0", "bootstrap": "^3.3.7", "core-js": "^2.5.4", "datatables.net": "^1.10.19", "datatables.net-dt": "^1.10.19", "datatables.net-responsive-dt": "^2.2.3", "exceljs": "^2.0.1", "file-saver": "^2.0.2", "font-awesome": "^4.7.0", "jquery": "^3.4.1", "moment": "^2.24.0", "mydatepicker": "^9.0.2", "ng-multiselect-dropdown": "^0.2.4", "ng2-file-upload": "^1.3.0", "ngx-image-cropper": "^1.4.1", "rxjs": "^6.5.4", "tslib": "^2.0.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.2.13", "@angular/cli": "~12.2.13", "@angular/compiler-cli": "~12.2.13", "@angular/language-service": "~12.2.13", "@types/datatables.net": "^1.10.17", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/jquery": "^3.3.30", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.9", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~7.0.0", "tslint": "~6.1.0", "typescript": "~4.3.5"}}