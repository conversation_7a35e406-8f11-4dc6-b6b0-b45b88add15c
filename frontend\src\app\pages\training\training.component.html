<div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Training Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'Training.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('TRAINING','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('TRAINING','AddButton')">
            <span class="hidden-xs">{{'Training.ADD_NEW_TRAINING' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Category.objName' | translate}} {{'Category.title' | translate}}</th>
					    		<th>{{'SubCategory.objName' | translate}} {{'SubCategory.title' | translate}}</th>
					    		<th>{{'Language.objName' | translate}} {{'Language.name' | translate}}</th>
					    		<th>{{'Training.videoTitle' | translate}}</th>
					    		<th>{{'Training.description' | translate}}</th>
					    		<th>{{'Training.videoUrl' | translate}}</th>
					    		<th>{{'Training.publishedForTrainingFeed' | translate}}</th>
					    		<th>{{'Training.commonVideoTitle' | translate}}</th>
					    		<th>{{'Training.groupCode' | translate}}</th>
					    		<th>{{'Training.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        		<td>
				                    		<a *ngIf="record.categoryIdDetail" class="primary-color" [routerLink]="['/dashboard/category/detail/'+record.categoryIdDetail.id]">
				                       		{{record.categoryIdDetail.title}}
						        		
						        		</a> 
					        		</td>
					        		<td>
				                    		<a *ngIf="record.subCategoryIdDetail" class="primary-color" [routerLink]="['/dashboard/subcategory/detail/'+record.subCategoryIdDetail.id]">
				                       		{{record.subCategoryIdDetail.title}}
						        		
						        		</a> 
					        		</td>
					        		<td>
				                    		<a *ngIf="record.languageIdDetail" class="primary-color" [routerLink]="['/dashboard/language/detail/'+record.languageIdDetail.id]">
				                       		{{record.languageIdDetail.name}}
						        		
						        		</a> 
					        		</td>
					        			<td>{{record.videoTitle}}</td>
					        		<td>
					        			<div [innerHtml]="record.description" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        		<td>
					        			<div [innerHtml]="record.videoUrl" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        	<td class="text-center">
			                    	<i class="fa" [ngClass]="{'fa-check enabled-icon':record.publishedForTrainingFeed,'fa-times disabled-icon':!record.publishedForTrainingFeed}"
			                        	aria-hidden="true"></i>
			                    </td>
					        		<td>
					        			<div [innerHtml]="record.commonVideoTitle" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        			<td>{{record.groupCode}}</td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">	         
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('TRAINING','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('TRAINING','DetailButton')"
				              (click)="loadDetailPage(record.id)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('TRAINING','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('TRAINING','EditButton')"
                           [routerLink]="['/dashboard/training/edit/'+record.id]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('TRAINING','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('TRAINING','DeleteButton')"
	                           (click)="remove(record.id)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>		
            </table>
    </div>
</div>

<app-training-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-training-edit>

<div class="modal fade site-detail-modal right" id="trainingDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="trainingDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-training-detail [recordId]="selectedId"></app-training-detail>
            </div>
        </div>
    </div>
</div>

