import { Routes } from '@angular/router';
import { LandingComponent } from '../landing/landing.component';
import { LayoutComponent } from './layout.component';
import { ChangePasswordComponent } from '../change-password/change-password.component';
import { AccountSettingsComponent } from '../account-settings/account-settings.component';
import { UsersComponent } from '../users/users.component';
import { UsersEditComponent } from '../users/edit/users-edit.component';
import { AuthGuard } from '../../shared/auth.guard';

// import for all entity pages

	import { FarmComponent } from './../farm/farm.component';
	import { FarmEditComponent } from './../farm/edit/farm-edit.component';
	import { FarmDetailComponent } from './../farm/detail/farm-detail.component';
	import { LanguageComponent } from './../language/language.component';
	import { LanguageEditComponent } from './../language/edit/language-edit.component';
	import { LanguageDetailComponent } from './../language/detail/language-detail.component';
	import { CategoryComponent } from './../category/category.component';
	import { CategoryEditComponent } from './../category/edit/category-edit.component';
	import { CategoryDetailComponent } from './../category/detail/category-detail.component';
	import { SubCategoryComponent } from './../subcategory/subcategory.component';
	import { SubCategoryEditComponent } from './../subcategory/edit/subcategory-edit.component';
	import { SubCategoryDetailComponent } from './../subcategory/detail/subcategory-detail.component';
	import { UserFarmComponent } from './../userfarm/userfarm.component';
	import { UserFarmEditComponent } from './../userfarm/edit/userfarm-edit.component';
	import { UserFarmDetailComponent } from './../userfarm/detail/userfarm-detail.component';
	import { TrainingComponent } from './../training/training.component';
	import { TrainingEditComponent } from './../training/edit/training-edit.component';
	import { TrainingDetailComponent } from './../training/detail/training-detail.component';
	import { UserAssignTrainingComponent } from './../userassigntraining/userassigntraining.component';
	import { UserAssignTrainingEditComponent } from './../userassigntraining/edit/userassigntraining-edit.component';
	import { UserAssignTrainingDetailComponent } from './../userassigntraining/detail/userassigntraining-detail.component';
	import { FarmAssignTrainingComponent } from './../farmassigntraining/farmassigntraining.component';
	import { FarmAssignTrainingEditComponent } from './../farmassigntraining/edit/farmassigntraining-edit.component';
	import { FarmAssignTrainingDetailComponent } from './../farmassigntraining/detail/farmassigntraining-detail.component';
	import { MomentComponent } from './../moment/moment.component';
	import { MomentEditComponent } from './../moment/edit/moment-edit.component';
	import { MomentDetailComponent } from './../moment/detail/moment-detail.component';
	import { UserTrainingStatusComponent } from './../usertrainingstatus/usertrainingstatus.component';
	import { UserTrainingStatusEditComponent } from './../usertrainingstatus/edit/usertrainingstatus-edit.component';
	import { UserTrainingStatusDetailComponent } from './../usertrainingstatus/detail/usertrainingstatus-detail.component';

export const LAYOUTROUTING: Routes = [
  {
    path: '', component: LayoutComponent, children: [
      { path: '', component: LandingComponent },
      { path: 'change/password', component: ChangePasswordComponent },
      { path: 'account/settings', component: AccountSettingsComponent },
      { path: 'users', component: UsersComponent, canActivate: [AuthGuard], data: {roles: ['ROLE_ADMIN']} },
      { path: 'user/edit/:id', component: UsersEditComponent, canActivate: [AuthGuard], data: {roles: ['ROLE_ADMIN']} },
        
      	{ 	path: 'farm', 
      		component: FarmComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'farm/edit/:id', 
      		component: FarmEditComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'farm/detail/:id', 
      		component: FarmDetailComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'language', 
      		component: LanguageComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'language/edit/:id', 
      		component: LanguageEditComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'language/detail/:id', 
      		component: LanguageDetailComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'category', 
      		component: CategoryComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'category/edit/:id', 
      		component: CategoryEditComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'category/detail/:id', 
      		component: CategoryDetailComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'sub-category', 
      		component: SubCategoryComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'sub-category/edit/:id', 
      		component: SubCategoryEditComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'sub-category/detail/:id', 
      		component: SubCategoryDetailComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'user-farm', 
      		component: UserFarmComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'user-farm/edit/:id', 
      		component: UserFarmEditComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'user-farm/detail/:id', 
      		component: UserFarmDetailComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'training', 
      		component: TrainingComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'training/edit/:id', 
      		component: TrainingEditComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'training/detail/:id', 
      		component: TrainingDetailComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'user-assign-training', 
      		component: UserAssignTrainingComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'user-assign-training/edit/:id', 
      		component: UserAssignTrainingEditComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'user-assign-training/detail/:id', 
      		component: UserAssignTrainingDetailComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'farm-assign-training', 
      		component: FarmAssignTrainingComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'farm-assign-training/edit/:id', 
      		component: FarmAssignTrainingEditComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'farm-assign-training/detail/:id', 
      		component: FarmAssignTrainingDetailComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'moment', 
      		component: MomentComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'moment/edit/:id', 
      		component: MomentEditComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'moment/detail/:id', 
      		component: MomentDetailComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'user-training-status', 
      		component: UserTrainingStatusComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'user-training-status/edit/:id', 
      		component: UserTrainingStatusEditComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	},
      	{ 	path: 'user-training-status/detail/:id', 
      		component: UserTrainingStatusDetailComponent, 
      		canActivate: [AuthGuard], 
      		data: {roles: ['ROLE_ADMIN']} 
      	}
      
    ]
  },
];
