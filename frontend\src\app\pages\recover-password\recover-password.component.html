<div class="login-box-container">
  <div class="col-md-12 no-padding" id="loginContainer">
    <div class="col-md-12 no-padding login-logo-container">
      <img src="/assets/images/logo.png" class="img-responsive login-logo">
    </div>
    <form class="login-form" #recoverForm="ngForm" novalidate="novalidate">
      <h3 class="margin-top-10 margin-bottom-20">{{"RECOVER PASSWORD" | translate}}</h3>
      <div class="form-item" [ngClass]="{'has-error':!password.valid && onClickValidation}">
        <h5>{{"PASSWORD" | translate}}</h5>
        <input class="form-control" type="password" placeholder="Enter New Password" name="password" #password="ngModel"
               [(ngModel)]="data.password" required="required" autofocus>
      </div>
      <div class="form-item margin-top-20"
           [ngClass]="{'has-error':(!confirmPassword.valid && onClickValidation) || data.confirmPassword !== data.password} ">
        <h5>{{"CONFIRM PASSWORD" | translate}}</h5>
        <input class="form-control" type="password" placeholder="Confirm Password" name="confirmPassword"
               #confirmPassword="ngModel" [(ngModel)]="data.confirmPassword" required="required">
      </div>
      <div class="clearfix"></div>
      <div class="margin-top-20">
        <button class="btn btn-primary site-button"
                (click)="resetPassword(recoverForm.form.valid)">{{"RECOVER PASSWORD" | translate}}</button>
      </div>
    </form>
  </div>
</div>
