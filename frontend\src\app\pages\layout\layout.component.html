<div class="wrapper">
  <nav id="sidebar">
    <div class="sidebar-header">
        <div class="logo">
            <img class="large-logo img-responsive" src="assets/images/large-logo.png"/>
            <img class="small-logo img-responsive" src="assets/images/mobile-logo.png"/>
        </div>
    </div>
    <ul class="menu-container nav-scroll">
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('COMMON','UserPage')">
        <a [class.disabled]="authService.isDisabled('COMMON','UserPage')" [routerLink]="['/dashboard/users']">
          <i class="fa fa-users"></i>{{'Users' | translate}}
        </a>
      </li>
      
          <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('FARM','Page')">
	    <a [class.disabled]="authService.isDisabled('FARM','Page')" [routerLink]="['/dashboard/farm']">
	      <i class="fa fa-user"></i>{{'Farm.objName' | translate}}
	    </a>
      </li>
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('LANGUAGE','Page')">
	    <a [class.disabled]="authService.isDisabled('LANGUAGE','Page')" [routerLink]="['/dashboard/language']">
	      <i class="fa fa-user"></i>{{'Language.objName' | translate}}
	    </a>
      </li>
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('CATEGORY','Page')">
	    <a [class.disabled]="authService.isDisabled('CATEGORY','Page')" [routerLink]="['/dashboard/category']">
	      <i class="fa fa-user"></i>{{'Category.objName' | translate}}
	    </a>
      </li>
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('SUBCATEGORY','Page')">
	    <a [class.disabled]="authService.isDisabled('SUBCATEGORY','Page')" [routerLink]="['/dashboard/sub-category']">
	      <i class="fa fa-user"></i>{{'SubCategory.objName' | translate}}
	    </a>
      </li>
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('USERFARM','Page')">
	    <a [class.disabled]="authService.isDisabled('USERFARM','Page')" [routerLink]="['/dashboard/user-farm']">
	      <i class="fa fa-user"></i>{{'UserFarm.objName' | translate}}
	    </a>
      </li>
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('TRAINING','Page')">
	    <a [class.disabled]="authService.isDisabled('TRAINING','Page')" [routerLink]="['/dashboard/training']">
	      <i class="fa fa-user"></i>{{'Training.objName' | translate}}
	    </a>
      </li>
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('USERASSIGNTRAINING','Page')">
	    <a [class.disabled]="authService.isDisabled('USERASSIGNTRAINING','Page')" [routerLink]="['/dashboard/user-assign-training']">
	      <i class="fa fa-user"></i>{{'UserAssignTraining.objName' | translate}}
	    </a>
      </li>
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('FARMASSIGNTRAINING','Page')">
	    <a [class.disabled]="authService.isDisabled('FARMASSIGNTRAINING','Page')" [routerLink]="['/dashboard/farm-assign-training']">
	      <i class="fa fa-user"></i>{{'FarmAssignTraining.objName' | translate}}
	    </a>
      </li>
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('MOMENT','Page')">
	    <a [class.disabled]="authService.isDisabled('MOMENT','Page')" [routerLink]="['/dashboard/moment']">
	      <i class="fa fa-user"></i>{{'Moment.objName' | translate}}
	    </a>
      </li>
      <li routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}" *ngIf="authService.isAccessible('USERTRAININGSTATUS','Page')">
	    <a [class.disabled]="authService.isDisabled('USERTRAININGSTATUS','Page')" [routerLink]="['/dashboard/user-training-status']">
	      <i class="fa fa-user"></i>{{'UserTrainingStatus.objName' | translate}}
	    </a>
      </li>
	<li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle" (click)="showSubMenu('SETTING')">
            <i class="fa fa-cog"></i>
            <span>{{'DASHBOARD.ACCOUNT_SETTINGS'|translate}}</span>
        </a>
        <ul class="nav-dropdown-items list-unstyled"
            [ngClass]="{'show-subMenu':isShowSubMenu['SETTING'], 'hide-subMenu':!isShowSubMenu['SETTING']}">
            <li routerLinkActive="active">
                <a [routerLink]="['/dashboard/account/settings']">
                    <i class="fa fa-user"></i>
                    <span>{{'DASHBOARD.PROFILE'|translate}}</span>
                </a>
            </li>
            <li routerLinkActive="active">
                <a [routerLink]="['/dashboard/change/password']">
                    <i class="fa fa-key"></i>
                    <span>{{'DASHBOARD.CHANGE_PASSWORD'|translate}}</span>
                </a>
            </li>
        </ul>
    </li>
	  <li class="nav-item dropdown">
	    <a class="nav-link dropdown-toggle" (click)="showSubMenu('REPORTS')">
	      <i class="fa fa-cube"></i> {{'COMMON.REPORT' | translate}}
	    </a>
	    <ul class="nav-dropdown-items list-unstyled components"
	      [ngClass]="{'show-subMenu':isShowSubMenu['REPORTS'], 'hide-subMenu':!isShowSubMenu['REPORTS']}">
	  	</ul>
	  </li>
	  <li (click)="authService.logout()">
	    <a>
	      <i class="fa fa-sign-out"></i>{{"DASHBOARD.LOGOUT" | translate}}
	    </a>
	  </li>
    </ul>
    <div class="user-info-container">
        <div class="user-icon-container">
            <img class="img-responsive" src="assets/images/mobile-logo.png"/>
        </div>
        <div class="user-profile-container">
            <div class="user-name">{{authService.getUser()?.fullName}}</div>
            <a (click)="authService.logout()">Logout</a>
        </div>
    </div>
  </nav>
  <div id="content" class="content-body">
    <div class="page-outer-container nav-scroll">
      <div class="page-inner-container">
        <router-outlet></router-outlet>
      </div>
    </div>
  </div>
</div>

<app-file-cropper></app-file-cropper>
<app-image-popup-model></app-image-popup-model>
