<div class="breadcrumb-container" *ngIf="!isPlusButton">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Moment Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li><a [routerLink]="['/dashboard/moment']">{{'Moment.objNames' | translate}}</a></li>
                <li class="active"
                    *ngIf="request.isNewRecord">{{"COMMON.NEW" | translate}} {{'Moment.objName' | translate}}</li>
                <li class="active" *ngIf="!request.isNewRecord">{{"COMMON.UPDATE" | translate}} {{moment.name}}</li>
            </ol>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container">
    <div class="site-card">
    	 <form #momentForm="ngForm" novalidate="novalidate">
            <div class="row justify-content-start">
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Moment.farmId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="farms"
			                                       bindLabel="name"
			                                       bindValue="id"
			                                       name="momentFarmId"
			                                       #momentFarmId="ngModel"
					                            		[(ngModel)]="moment.farmId" required="required" #FarmId="ngModel"
			                                       [ngClass]="{'invalid-field':momentFarmId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} farm">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('momentFarmIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Moment.type" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="100"  name="momentType" required="required" [(ngModel)]="moment.type" #Type="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Moment.description" | translate}}
		                        </label>
				                    	<textarea class="form-control" rows="5" name="momentDescription"  [(ngModel)]="moment.description" #Description="ngModel"></textarea>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Moment.mediaType" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="255"  name="momentMediaType"  [(ngModel)]="moment.mediaType" #MediaType="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Moment.mediaUrl" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="MAX"  name="momentMediaUrl"  [(ngModel)]="moment.mediaUrl" #MediaUrl="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Moment.assignTrainingId" | translate}}
		                        </label>
									<div class="select-width" [ngClass]="{'input-group': !isPlusButton}">
										<ng-select [items]="userAssignTrainings"
			                                       bindLabel="id"
			                                       bindValue="id"
			                                       name="momentAssignTrainingId"
			                                       #momentAssignTrainingId="ngModel"
					                            		[(ngModel)]="moment.assignTrainingId"  #AssignTrainingId="ngModel"
			                                       [ngClass]="{'invalid-field':momentAssignTrainingId.invalid && onClickValidation}"
			                                       required="required"
			                                       placeholder="{{'COMMON.SELECT_OPTION' | translate}} userAssignTraining">
			                            </ng-select>
			                            <span class="input-group-btn" *ngIf="!isPlusButton">
			                                <button class="btn btn-primary" type="button" (click)="loadAssociatedPopup('momentAssignTrainingIdPopup')"><span
			                                        class="glyphicon glyphicon-plus"></span></button>
			                            </span>
			                        </div>
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Moment.userVideo" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="MAX"  name="momentUserVideo"  [(ngModel)]="moment.userVideo" #UserVideo="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Moment.instruction" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="MAX"  name="momentInstruction"  [(ngModel)]="moment.instruction" #Instruction="ngModel"> 
									</div>										 
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Moment.completedBy" | translate}}
		                        </label>
									<input class="form-control" type="number" min="0" name="momentCompletedBy"  [(ngModel)]="moment.completedBy" #CompletedBy="ngModel">
							</div>
						</div>
            			<div class="col-md-6">
							<div class="form-group" >
								<label class="control-label">
		                            {{"Moment.status" | translate}}
		                        </label>
									<div class="color-picker-input">
										<input class="form-control" type="text"  minlength="0" maxlength="255"  name="momentStatus"  [(ngModel)]="moment.status" #Status="ngModel"> 
									</div>										 
							</div>
						</div>
            </div>
        </form>
        <div class="clearfix"></div>
        <div class="col-md-12 no-padding text-right">
           <button title="Save" class="btn btn-primary site-button" type="button" (click)="save(momentForm.form)"
	        *ngIf="authService.isAccessible('MOMENT','AddButton')" 
	        [disabled]="authService.isDisabled('MOMENT','AddButton')">
	            {{"COMMON.SAVE" | translate}}
	        </button>
	        <button title="Cancel" class="btn btn-default site-cancel-button margin-left-10" type="button" (click)="navigate()">
	            {{"COMMON.CANCEL" | translate}}
	        </button>
            <div class="clearfix"></div>
        </div>
        <div class="clearfix"></div>
    </div>
    <div class="clearfix"></div>
</div>
		<div class="modal fade nav-scroll" id="momentFarmIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-farm [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-farm>
		            </div>
		        </div>
		    </div>
		</div>
		<div class="modal fade nav-scroll" id="momentAssignTrainingIdPopup" role="dialog">
		    <div class="modal-dialog associated-dialog">
		        <div class="modal-content">
		             <div class="modal-body" *ngIf="request.isShowAssociated">
		                 <button type="button" class="close" data-dismiss="modal">&times;</button>
		                <app-userassigntraining [onAssociatedValueSelected]="onAssociatedValueSelected.bind(this)"></app-userassigntraining>
		            </div>
		        </div>
		    </div>
		</div>

