import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Users } from './users';
import { Farm } from './farm';
export class UserFarm extends BaseModel {
	
				tenantId: number;
			  	slug: string;
			  	userIdDetail: Users;
			  	userId: string;
			  	farmIdDetail: Farm;
			  	farmId: string;
	
    constructor() {
        super();
			this.isDeleted=false;
			this.isActive=true;
    }
    
   static fromResponse(data: any) : UserFarm {
		const obj = new UserFarm();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
	  	obj.userIdDetail = data.userIdDetail;
	  	obj.userId = data.userId;
	  	obj.farmIdDetail = data.farmIdDetail;
	  	obj.farmId = data.farmId;
		return obj;
	}

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
        return true;
    }

    forRequest() {
        return this;
    }
}
