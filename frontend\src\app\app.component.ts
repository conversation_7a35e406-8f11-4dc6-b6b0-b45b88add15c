import {Component, ViewEncapsulation} from '@angular/core';
import {TranslateService} from '@ngx-translate/core';
import { RoutingState } from './shared/routing-state.service';

@Component({
  selector: '.app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppComponent {
  title = 'Welcome to Application';

  constructor(private translate: TranslateService, private routingState: RoutingState) {
    translate.setDefaultLang('en');
    this.routingState.loadRouting();
  }
}
