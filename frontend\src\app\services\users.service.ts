import {Injectable} from '@angular/core';
import {HttpServiceRequests} from '../shared/http.service';
import {IResourceWithId, RestResponse} from '../shared/auth.model';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {BaseService} from '../config/base.service';
import {FilterParam} from '../models/filterparam';

@Injectable({
    providedIn: 'root'
})
export class UsersService extends BaseService {

    constructor(public http: HttpClient) {
        super(http, '/api/account/user', '/api/account/users');
    }

    fetchUser(id: number): Observable<RestResponse> {
        return this.getRecord('/api/account/user/' + id);
    }

    save(data: any): Promise<RestResponse> {
        return this.saveRecord('/api/account/register', data);
    }

    update(data: any): Promise<RestResponse> {
        return this.updateRecord('/api/account/update', data);
    }

    GetAllRoles(data: any): Promise<RestResponse> {
        return this.getRecords('/api/account/Roles', data);
    }

    fetchAllUsers(filterParam: FilterParam): Promise<RestResponse> {
        return this.getRecords('/api/account/GetCompanies', filterParam);
    }
}
