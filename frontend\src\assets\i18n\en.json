{"PROFILE": "Profile", "ENGLISH": "English", "ITALIAN": "Italian", "LOGOUT": "Logout", "CATEGORIES": "Categories", "ACCOUNT SETTINGS": "Account settings", "SETTINGS": "Settings", "FIRST NAME": "First name", "LAST NAME": "Last name", "EMAIL": "Email", "MOBILE NO": "Mobile", "SAVE": "Save", "CHANGE PASSWORD": "Change password", "OLD PASSWORD": "Current password", "NEW PASSWORD": "New password", "CONFIRM NEW PASSWORD": "Confirm new password", "RECOVER PASSWORD": "Reset", "PASSWORD": "Password", "CONFIRM PASSWORD": "Confirm password", "SIGN IN": "Log In", "STAY SIGNED IN": "Stay signed in", "FORGOT PASSWORD": "Forgot password", "RESET": "Reset", "BACK TO LOGIN": "Back to login", "ADMIN": "ADMIN", "ADMIN_SHORT": "AD", "WELCOME": "Welcome", "LOGIN": {"EMAIL": "Email", "PASSWORD": "Password", "FORGOT_PASSWORD": "Forgot password", "SIGN_IN": "Log In", "BACK_TO_LOGIN": "Back to login", "RESET": "Reset", "RECOVER_PASSWORD": "Recover Password"}, "EDIT": {"BASIC_INFO": "Basic Info", "ACTIVE": "Active", "ADD_NEW_RECORD": "Add New Record"}, "DASHBOARD": {"ACCOUNT_SETTINGS": "Profile", "CHANGE_PASSWORD": "Change Password", "LOGOUT": "Logout", "CONTACT": "Contact", "objName": "Dashboard", "PROFILE": "Profile"}, "COMMON": {"ADD": "Add", "NEW": "New", "UPDATE": "Update", "EDIT": "Edit", "DELETE": "Delete", "HOME": "Home", "CANCEL": "Cancel", "SAVE": "Save", "SELECT": "Select", "SELECT_OPTION": "Select Option", "RUNREPORT": "Run Report", "REPORT": "Report", "DETAIL": "Detail", "EXCELREPORT": "Export as EXCEL", "PDFREPORT": "Export as PDF", "PRINTREPORT": "Print Report", "NORECORDS": "No Records Available", "ACTION": "Action", "YES": "Yes", "NO": "No", "BACK": "Back", "CROP_IMAGES": "Crop Images", "APPLY_CROP": "Apply Changes", "UPLOAD_ALL": "Upload All"}, "USERS": {"objNames": "Users", "ADD_NEW_USERS": "Add New User", "NEW_USER": "New User", "Name": "Name", "FirstName": "First Name", "LastName": "Last Name", "Email": "Email", "Role": "Role", "SelectRole": "Select Role", "PhoneNumber": "Phone Number", "Active": "Active", "Action": "Action", "YES": "Yes", "NO": "No"}, "Farm": {"objName": "Farm", "objNames": "Farms", "Detail": "Farm Detail", "ADD_NEW_FARM": "Add New Farm", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "name": "Name", "country": "Country", "latitude": "Latitude", "longitude": "Longitude", "description": "Description", "propertyNo": "Property No", "farmingUrl": "Farming Url"}, "Language": {"objName": "Language", "objNames": "Languages", "Detail": "Language Detail", "ADD_NEW_LANGUAGE": "Add New Language", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "name": "Name", "country": "Country"}, "Category": {"objName": "Category", "objNames": "Categories", "Detail": "Category Detail", "ADD_NEW_CATEGORY": "Add New Category", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "title": "Title", "description": "Description", "languageId": "LanguageId", "commonTitle": "Common Title", "GROUPCODE_": "", "groupCode": "Group Code"}, "SubCategory": {"objName": "Sub Category", "objNames": "Sub Categories", "Detail": "Sub Category Detail", "ADD_NEW_SUBCATEGORY": "Add New Sub Category", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "categoryId": "Category", "title": "Title", "description": "Description", "languageId": "Language", "commonTitle": "Common Title", "groupCode": "Group Code"}, "UserFarm": {"objName": "UserFarm", "objNames": "User Farms", "Detail": "UserFarm Detail", "ADD_NEW_USERFARM": "Add New UserFarm", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "userId": "User", "farmId": "Farm"}, "Training": {"objName": "Training", "objNames": "Trainings", "Detail": "Training Detail", "ADD_NEW_TRAINING": "Add New Training", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "categoryId": "Category", "subCategoryId": "Sub Category", "languageId": "Language", "videoTitle": "Video Title", "description": "Description", "videoUrl": "Video Url", "publishedForTrainingFeed": "Published For Training Feed", "commonVideoTitle": "Common Video Title", "groupCode": "Group Code"}, "UserAssignTraining": {"objName": "User Assign Trainings", "objNames": "User Assign Trainings", "Detail": "User Assign Trainings Detail", "ADD_NEW_USERASSIGNTRAINING": "Add New User Assign Trainings", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "userId": "User", "trainingId": "Training", "status": "Status", "userVideoUrl": "User Video Url", "assignedDate": "Assigned Date", "farmId": "Farm"}, "FarmAssignTraining": {"objName": "Farm Assign Trainings", "objNames": "Farm Assign Trainings", "Detail": "Farm Assign Trainings Detail", "ADD_NEW_FARMASSIGNTRAINING": "Add New Farm Assign Trainings", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "trainingId": "Training", "farmId": "Farm", "assignedDate": "Assigned Date", "allUserAssigned": "All User Assigned"}, "Moment": {"objName": "Moment", "objNames": "Moments", "Detail": "Moment Detail", "ADD_NEW_MOMENT": "Add New Moment", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "farmId": "Farm", "type": "Type", "description": "Description", "mediaType": "Media Type", "mediaUrl": "Media Url", "assignTrainingId": "Assign Training", "userVideo": "User Video", "instruction": "Instruction", "completedBy": "Completed By", "status": "Status"}, "UserTrainingStatus": {"objName": "User Training Status", "objNames": "User Training Status", "Detail": "User Training Status Detail", "ADD_NEW_USERTRAININGSTATUS": "Add New User Training Status", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "userId": "User", "farmId": "Farm", "traningId": "Traning", "rejectCompletionDate": "Reject Completion Date", "userVideoUrl": "User Video Url", "isApproved": "Is Approved", "assignedDate": "Assigned Date"}, "ErrorCodeMessages": {"1001": "Some Error during selection of 'Users'", "1002": "Some Error during fetch of 'Users'", "1003": "Some Error during creation of new 'Users'", "1004": "Some Error during modification of 'Users'", "1005": "Some Error during removal of 'Users'", "2001": "Some Error during selection of 'Farms'", "2002": "Some Error during fetch of 'Farm'", "2003": "Some Error during creation of new 'Farm'", "2004": "Some Error during modification of 'Farm'", "2005": "Some Error during removal of 'Farm'", "3001": "Some Error during selection of 'Languages'", "3002": "Some Error during fetch of 'Language'", "3003": "Some Error during creation of new 'Language'", "3004": "Some Error during modification of 'Language'", "3005": "Some Error during removal of 'Language'", "4001": "Some Error during selection of 'Categories'", "4002": "Some Error during fetch of 'Category'", "4003": "Some Error during creation of new 'Category'", "4004": "Some Error during modification of 'Category'", "4005": "Some Error during removal of 'Category'", "5001": "Some Error during selection of 'Sub Categories'", "5002": "Some Error during fetch of 'Sub Category'", "5003": "Some Error during creation of new 'Sub Category'", "5004": "Some Error during modification of 'Sub Category'", "5005": "Some Error during removal of 'Sub Category'", "6001": "Some Error during selection of 'User Farms'", "6002": "Some Error during fetch of 'UserFarm'", "6003": "Some Error during creation of new 'UserFarm'", "6004": "Some Error during modification of 'UserFarm'", "6005": "Some Error during removal of 'UserFarm'", "7001": "Some Error during selection of 'Trainings'", "7002": "Some Error during fetch of 'Training'", "7003": "Some Error during creation of new 'Training'", "7004": "Some Error during modification of 'Training'", "7005": "Some Error during removal of 'Training'", "8001": "Some Error during selection of 'User Assign Trainings'", "8002": "Some Error during fetch of 'User Assign Trainings'", "8003": "Some Error during creation of new 'User Assign Trainings'", "8004": "Some Error during modification of 'User Assign Trainings'", "8005": "Some Error during removal of 'User Assign Trainings'", "9001": "Some Error during selection of 'Farm Assign Trainings'", "9002": "Some Error during fetch of 'Farm Assign Trainings'", "9003": "Some Error during creation of new 'Farm Assign Trainings'", "9004": "Some Error during modification of 'Farm Assign Trainings'", "9005": "Some Error during removal of 'Farm Assign Trainings'", "10001": "Some Error during selection of 'Moments'", "10002": "Some Error during fetch of 'Moment'", "10003": "Some Error during creation of new 'Moment'", "10004": "Some Error during modification of 'Moment'", "10005": "Some Error during removal of 'Moment'", "11001": "Some Error during selection of 'User Training Status'", "11002": "Some Error during fetch of 'User Training Status'", "11003": "Some Error during creation of new 'User Training Status'", "11004": "Some Error during modification of 'User Training Status'", "11005": "Some Error during removal of 'User Training Status'"}}