@import "variables";


.margin-top-5 {
    margin-top: 5px !important;
}

.margin-top-8 {
    margin-top: 8px !important;
}

.margin-top-10 {
    margin-top: 10px !important;
}

.margin-top-15 {
    margin-top: 15px !important;
}

.margin-top-20 {
    margin-top: 20px !important;
}

.margin-top-25 {
    margin-top: 25px !important;
}

.margin-top-30 {
    margin-top: 30px !important;
}

.margin-top-35 {
    margin-top: 35px !important;
}

.margin-top-40 {
    margin-top: 40px !important;
}

.margin-top-45 {
    margin-top: 45px !important;
}

.margin-top-50 {
    margin-top: 50px !important;
}

.margin-top-55 {
    margin-top: 55px !important;
}

.margin-top-60 {
    margin-top: 60px !important;
}

.margin-top-70 {
    margin-top: 70px !important;
}

.margin-top-80 {
    margin-top: 80px !important;
}

/*------------*/

.margin-bottom-5 {
    margin-bottom: 5px !important;
}

.margin-bottom-10 {
    margin-bottom: 10px !important;
}

.margin-bottom-15 {
    margin-bottom: 15px !important;
}

.margin-bottom-20 {
    margin-bottom: 20px !important;
}

.margin-bottom-25 {
    margin-bottom: 25px !important;
}

.margin-bottom-30 {
    margin-bottom: 30px !important;
}

.margin-bottom-35 {
    margin-bottom: 35px !important;
}

.margin-bottom-40 {
    margin-bottom: 40px !important;
}

.margin-bottom-45 {
    margin-bottom: 45px !important;
}

.margin-bottom-50 {
    margin-bottom: 50px !important;
}

.margin-bottom-55 {
    margin-bottom: 55px !important;
}

.margin-bottom-60 {
    margin-bottom: 60px !important;
}

.margin-bottom-70 {
    margin-bottom: 70px !important;
}

.margin-bottom-80 {
    margin-bottom: 80px !important;
}

/*--------------*/

.margin-left-5 {
    margin-left: 5px !important;
}

.margin-left-10 {
    margin-left: 10px !important;
}

.margin-left-15 {
    margin-left: 15px !important;
}

.margin-left-20 {
    margin-left: 20px !important;
}

.margin-left-25 {
    margin-left: 25px !important;
}

.margin-left-30 {
    margin-left: 30px !important;
}

.margin-left-35 {
    margin-left: 35px !important;
}

.margin-left-40 {
    margin-left: 40px !important;
}

.margin-left-45 {
    margin-left: 45px !important;
}

.margin-left-50 {
    margin-left: 50px !important;
}

.margin-left-55 {
    margin-left: 55px !important;
}

.margin-left-60 {
    margin-left: 60px !important;
}

/*--------------*/

.margin-right-5 {
    margin-right: 5px !important;
}

.margin-right-10 {
    margin-right: 10px !important;
}

.margin-right-15 {
    margin-right: 15px !important;
}

.margin-right-20 {
    margin-right: 20px !important;
}

.margin-right-25 {
    margin-right: 25px !important;
}

.margin-right-30 {
    margin-right: 30px !important;
}

.margin-right-35 {
    margin-right: 35px !important;
}

.margin-right-40 {
    margin-right: 40px !important;
}

.margin-right-45 {
    margin-right: 45px !important;
}

.margin-right-50 {
    margin-right: 50px !important;
}

.margin-right-55 {
    margin-right: 55px !important;
}

.margin-right-60 {
    margin-right: 60px !important;
}

/*------------*/

.no-margin-left {
    margin-left: 0px !important;
}

.no-margin-bottom {
    margin-bottom: 0px !important;
}

.no-margin-top {
    margin-top: 0px !important;
}

.no-margin-right {
    margin-right: 0px !important;
}

.no-margin {
    margin: 0px !important;
}

/*End Margin Classes*/


/*Padding Classes*/

.no-padding {
    padding: 0px !important;
}

.no-padding-left {
    padding-left: 0px !important;
}

.no-padding-right {
    padding-right: 0px !important;
}

.no-padding-top {
    padding-top: 0px !important;
}

.no-padding-bottom {
    padding-bottom: 0px !important;
}

.padding-5 {
    padding: 5px;
}

.padding-10 {
    padding: 10px;
}

.padding-15 {
    padding: 15px;
}

.padding-20 {
    padding: 20px !important;
}

.padding-left-5 {
    padding-left: 5px;
}

.padding-left-10 {
    padding-left: 10px;
}

.padding-left-15 {
    padding-left: 15px;
}

.padding-left-20 {
    padding-left: 20px;
}

.padding-right-5 {
    padding-right: 5px;
}

.padding-right-10 {
    padding-right: 10px;
}

.padding-right-15 {
    padding-right: 15px;
}

.padding-right-20 {
    padding-right: 20px;
}

.padding-top-5 {
    padding-top: 5px;
}

.padding-top-10 {
    padding-top: 10px;
}

.padding-top-15 {
    padding-top: 15px;
}

.padding-top-20 {
    padding-top: 20px;
}

.padding-bottom-5 {
    padding-bottom: 5px;
}

.padding-bottom-10 {
    padding-bottom: 10px;
}

.padding-bottom-15 {
    padding-bottom: 15px;
}

.padding-bottom-20 {
    padding-bottom: 20px;
}

.disable {
    display: none;
}

html {
    min-height: 100%;
    position: relative;
    height: 100%;
}

body {
    font-family: $font-family;
    font-weight: 400;
    min-height: 100%;
}

p {
    font-family: $font-family;
    font-size: 1.1em;
    font-weight: 300;
    line-height: 1.7em;
    color: #222222;
    word-break: break-all;
}

.app-root {
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
}

a,
a:hover,
a:focus {
    color: inherit;
    text-decoration: none;
    transition: all 0.3s;
    cursor: pointer !important;
}

/*Start Third Party Plugin CSS*/

.notify-alert {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.23), 0 3px 10px rgba(0, 0, 0, 0.16);
    border: 0 !important;
    max-width: 400px;
    color: $other-background-color;

    &.alert-success {
        background-color: #4caf50;
    }

    &.alert-info {
        background-color: #689ab5;
    }

    &.alert-warning {
        background-color: #ffad33;
    }

    &.alert-danger {
        background-color: #f55a4e;
    }

    button[data-notify=dismiss] {
        margin-left: 5px;
        outline: none !important;
    }
}

.material-switch {
    display: block;
    height: 40px;
    padding-top: 10px;

    & > input[type="checkbox"] {
        display: none;
    }

    & > label {
        cursor: pointer;
        height: 0px;
        position: relative;
        width: 40px;

        &::before {
            background: black;
            box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            content: '';
            height: 16px;
            margin-top: -8px;
            position: absolute;
            opacity: 0.3;
            transition: all 0.4s ease-in-out;
            width: 40px;
            left: 0px;
        }

        &::after {
            background: white;
            border-radius: 16px;
            box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
            content: '';
            height: 24px;
            left: -4px;
            margin-top: -8px;
            position: absolute;
            top: -4px;
            transition: all 0.3s ease-in-out;
            width: 24px;
        }
    }

    & > input[type="checkbox"]:checked + label::before {
        background: inherit;
        opacity: 0.5;
    }

    & > input[type="checkbox"]:checked + label::after {
        background: inherit;
        left: 20px;
    }
}

.sweet-alert {
    .sa-icon {
        display: none !important;
    }

    h2 {
        color: #111 !important;
        font-weight: 500;
        font-family: 'Poppins', sans-serif !important;
    }

    button {
        display: inline-block;
        padding: 6px 12px;
        margin-bottom: 0;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.42857143;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        -ms-touch-action: manipulation;
        touch-action: manipulation;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-image: none;
        border: 1px solid transparent;
        border-radius: 4px;
        margin: 26px 23px 0 23px !important;

        &.cancel {
            color: $background-color !important;
            background-color: $cancel-btn-color !important;
            border-color: $background-color !important;

            &:hover {
                color: $background-color !important;
                background-color: $cancel-btn-color !important;
                border-color: $background-color !important;
            }

            &:hover,
            &:focus,
            &:active {
                text-decoration: none !important;
            }
        }
    }
}

.nav-scroll {
    &::-webkit-scrollbar-track {
        -webkit-box-shadow: $scroll-bar-box-shadow;
        box-shadow: $scroll-bar-box-shadow;
        border-radius: 10px;
        background-color: #F5F5F5;

    }

    &::-webkit-scrollbar {
        width: 5px;
        background-color: #F5F5F5;
        height: 5px;
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        -webkit-box-shadow: $scroll-bar-box-shadow;
        box-shadow: $scroll-bar-box-shadow;
        background-color: $scroll-bar-color;
    }
}

.loader-container {
    width: 50px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -25px;
    margin-top: -25px;

    .loader {
        border: 2px solid $loader-color;
        -webkit-animation: spin 1s linear infinite;
        animation: spin 1s linear infinite;
        border-top: 5px solid $other-background-color;
        border-radius: 50%;
        width: 50px;
        height: 50px;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/*End Third Party Plugin CSS*/

/* ---------------------------------------------------
    SIDEBAR STYLE
----------------------------------------------------- */

.wrapper {
    display: flex;
    align-items: stretch;
}

#sidebar {
    min-width: 12%;
    max-width: 12%;
    background: $site-menu-background-color;
    color: $site-menu-color;
    transition: all 0.3s;
    position: relative;

    &.active {
        min-width: 6%;
        max-width: 6%;
        text-align: center;

        .large-logo {
            display: none !important;
        }

        .small-logo {
            display: block !important;
        }

        .sidebar-header {
            h3 {
                display: none;
            }
        }

        strong {
            display: block;
        }

        ul {
            li {
                a {
                    padding: 20px 5px;
                    text-align: center;
                    font-size: 10px;
                    text-overflow: ellipsis;
                    width: 100%;
                    white-space: nowrap;
                    overflow: hidden;

                    i {
                        margin-right: 0;
                        display: block;
                        font-size: 1.8em;
                        margin-bottom: 5px;
                    }
                }

                span {
                    display: none !important;
                }
            }
        }

        a[aria-expanded="false"]::before,
        a[aria-expanded="true"]::before {
            top: auto;
            bottom: 5px;
            right: 50%;
            -webkit-transform: translateX(50%);
            -ms-transform: translateX(50%);
            transform: translateX(50%);
        }

        .menu {
            display: none !important;
        }

        .user-profile-container {
            display: none !important;
        }
    }

    ul {
        li {
            a {
                text-align: left;
                padding: 14px 10px 14px 20px;
                font-size: 13px;
                display: block;

                &:hover {
                    color: $font-normal-color;
                }

                i {
                    margin-right: 10px;
                }
            }

            &.active > a, &.active > a i {
                -webkit-background-clip: text !important;
                background: transparent linear-gradient(180deg, #EE6072 0%, #F10AA0 100%) 0% 0% no-repeat padding-box;
                -webkit-text-fill-color: transparent;
            }
        }

        &.menu-container {
            padding: 0px;
            height: calc(100vh - 145px);
            overflow-y: auto;
            margin-bottom: 0px !important;
        }

        ul a {
            text-align: left;
            padding: 10px 10px 10px 20px;
            font-size: 12px;
            display: block;
            padding-left: 28px !important;
        }
    }

    .sidebar-header {
        padding: 20px;
        color: $other-background-color;
        border-bottom: .2px solid gray;
        position: relative;

        strong {
            display: none;
            font-size: 1.8em;
        }

        .logo {
            .large-logo {
                width: 115px;
                display: block;
            }

            .small-logo {
                width: 51px;
                margin: 0 auto;
                display: none;
            }

            .menu {
                font-size: 18px;
                position: absolute;
                right: 11px;
                top: 50%;
                margin-top: -13px;
            }
        }
    }


    .user-info-container {
        position: absolute;
        bottom: 0px;
        left: 0px;
        width: 100%;
        padding: 10px;
        text-transform: capitalize;
        border-top: .2px solid gray;
        height: 54px;
        height: 54px;

        .user-icon-container {
            display: inline-block;
            vertical-align: middle;
            width: 30px;
        }

        .user-profile-container {
            display: inline-block;
            vertical-align: middle;
            width: calc(100% - 35px);
            margin-left: 5px;

            .user-name {
                font-size: 12px;
            }
        }
    }
}

/* ---------------------------------------------------
    CONTENT STYLE
----------------------------------------------------- */

#content {
    padding: 0px;
    min-height: 100vh;
    transition: all 0.3s;

    &.mobile-content-body {
        width: 94%;
    }

    &.window-content-body {
        width: 88%;
    }

    &.small {
        width: calc(100% - 80px);
    }
}

.breadcrumb > li + li:before {
    content: ">" !important;
    color: black !important;
}

.breadcrumb-container {
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    background-color: #fff !important;

    .breadcrumb-detail-container {
        padding: 0px !important;
    }

    .project-name {
        margin: 0px !important;
        color: #F10AA0 !important;
    }

    .breadcrumb {
        background: #fff;
        margin-bottom: 0px !important;
        padding: 0px !important;

        li {
            font-size: 12px;
            font-weight: 500;

            &.active {
                color: #000000 !important
            }

        }
    }

    .menu-icon-button {
        display: inline-block !important;
        vertical-align: middle !important;
        margin-right: 20px;

        img {
            width: 20px;
        }
    }

    .project-name-container {
        display: inline-block !important;
        vertical-align: middle !important;
        border-left: 1px solid #bbb;
        padding-left: 10px;
    }
}

.page-outer-container {
    padding: 0px !important;
    background-color: $site-background-color;
    height: 100%;
    overflow: hidden !important;

    .page-inner-container {
        height: 100%;

        .page-header-container {
            border-bottom: 4px solid #f8f8f8;
        }

        .text-container {
            padding: 20px;
        }
    }
}

.mydp {
    border-radius: 0px !important;
    height: 48px !important;

    input {
        height: 46px !important;
    }

    .selbtngroup {
        height: 45px !important;
    }
}

.site-button {
    background-color: #5d78ff !important;
    border-color: #5d78ff !important;
    transition: all .3s ease 0s;
    padding: .65rem 15px;
    line-height: 1.5;
    border-radius: .25rem;
    font-weight: 400;
    height: 40px;

    &:active, &:hover {
        color: #fff !important;
        background-color: #3758ff !important;
        border-color: #2a4eff !important;
    }
}


.site-cancel-button {
    transition: all .3s ease 0s;
    padding: .65rem 15px;
    line-height: 1.5;
    border-radius: .25rem;
    font-weight: 400;
    background-color: transparent;
    border: 1px solid #e2e5ec;
    color: #595d6e;
    height: 40px;

    &:active, &:hover {
        color: #212529 !important;
        background-color: #c0c0dd !important;
        border-color: #b7b7d9 !important;
    }
}

.task-detail-page {
    .page-outer-container {
        background-color: #f4f7fc !important;
    }
}

.base-page-container {
    height: 100%;
    width: 100%;
    position: fixed;
    z-index: 1041;
}

.landing-page-container {
    background-color: #ffff !important;
}

.bold {
    font-weight: bold;
}

.capitalize {
    text-transform: capitalize;
}

.text-lowercase {
    text-transform: lowercase !important;
}

.btn[disabled] {
    pointer-events: none !important;
    cursor: pointer !important;
}

.form-control {
    height: 48px !important;
    border-radius: 0px;
}

select {
    border-radius: 0px;
}

textarea.form-control {
    height: auto !important;
}

.input-group-btn {
    .btn {
        height: 48px !important;
    }
}

.control-label {
    font-size: 12px;
    font-weight: 700;
}

.dt-bootstrap {
    .form-control {
        height: 30px !important;
    }
}

.page-error {
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -88px;
    margin-left: -138px;
    text-align: center;

    h1 {
        font-size: 53px;
        color: $background-color;
        text-shadow: 4px 4px 0 $other-background-color, 5px 6px 0 black;
    }
}

.fileinput-button {
    position: relative;
    overflow: hidden;
    display: inline-block;

    input {
        cursor: pointer;
        direction: ltr;
        height: 20px;
        margin: 0;
        opacity: 0;
        position: absolute;
        right: 0;
        top: 0.5px;
        width: 125px;
    }
}

.modal {
    .modal-header {
        background-color: #f1f1f1 !important;
        color: #000000 !important;
        padding: 20px;
        font-size: 17px;

        model-title {
            font-weight: 300;
        }
    }

    .modal-content {
        border-radius: 0;
    }

    &.site-detail-modal {
        .modal-dialog {
            margin: 0 !important;
            position: absolute !important;
            right: 0 !important;
            width: 800px;

            &.large {
                width: 70%;
            }
        }

        .modal-body {
            height: calc(100vh - 2px) !important;
            overflow-y: auto;
            padding: 0px !important;

            &::-webkit-scrollbar {
                width: 12px;
                height: 6px;
            }

            &::-webkit-scrollbar-track {
                border-radius: 10px;
                box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
                -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            }

            &::-webkit-scrollbar-thumb {
                border-radius: 10px;
                box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
                -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
            }
        }
    }
}

.modal.site-detail-modal.fade:not(.in).right .modal-dialog {
    -webkit-transform: translate3d(25%, 0, 0);
    transform: translate3d(25%, 0, 0);
}

.has-error .form-control,
.has-error.form-control {
    border-width: 2px !important;
    border-color: #a94442;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}

.image-container {
    white-space: nowrap;
    overflow-x: auto;
    max-width: 550px;

    .file-input {
        display: none !important;
    }

    .image-preview {
        display: inline-block;
        vertical-align: middle;
        width: 150px;
        margin-top: 10px;
        margin-bottom: 10px;
        position: relative;
        margin-right: 10px;
        border: 1px solid #ddd;

        &.hide {
            display: none !important;
        }

        .preview-image {
            width: 100%;
        }
    }

    .remove-icon {
        position: absolute;
        right: 5px;
        top: 5px;
        background-color: #222;
        padding: 3px;
        border-radius: 50%;
        font-size: 12px;
        color: $other-background-color;
        cursor: pointer;

        &::before {
            margin-left: 1px;
        }
    }

    .add-image-selection {
        width: 150px;
        height: 80px;
        cursor: pointer;

        .new-image {
            position: absolute;
            left: 50%;
            top: 50%;
            margin-left: -8px;
            margin-top: -12px;
            width: 24px;
            height: 24px;
        }
    }


    &::-webkit-scrollbar {
        width: 12px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        border-radius: 10px;
        box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    }

    &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
    }

    .selected-crop-image {
        border: 3px solid #1c7430;
    }
}

table {
    border-bottom: 0px !important;

    thead {
        th {
            font-size: 12px;
            text-transform: capitalize;
            background-color: #f1f1f1 !important;
            border: 1px solid #ddd !important;
        }
    }

    tbody {
        tr {
            td {
                padding: 10px !important;
                border: 1px solid #ddd !important;

                a {
                    color: #337ab7;
                }
            }
        }
    }
}

.site-page-container {
    padding: 20px;
    min-height: calc(100vh - 80px);

    .site-card {
        background: #fff;
        box-shadow: 0 0 13px 0 rgba(82, 63, 105, .1);
        border-radius: 6px;
        padding: 15px;
        border: 1px solid #fff;
        color: #282a3c;
    }

    .action-button {
        color: #93a2dd !important;
        font-size: 15px;

        &:nth-child(2) {
            margin-right: 5px;
            margin-left: 5px;
        }
    }
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0px !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: transparent !important;
    background: none !important;
    border: 1px solid #ddd !important;
}

table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > td:first-child, table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > th:first-child {
    padding-left: 30px !important;
}

/* ---------------------------------------------------
    MEDIAQUERIES
----------------------------------------------------- */

@media (max-width: 768px) {
    #content {
        &.window-content-body {
            width: 100%;
        }
    }
    #sidebar {
        min-width: 80px;
        max-width: 80px;
        text-align: center;
        margin-left: -80px !important;

        &.active {
            min-width: 15% !important;
            max-width: 15% !important;
            margin-left: 0 !important;

            ul {
                li {
                    a {
                        padding: 15px 5px !important;
                    }
                }
            }
        }
    }
    .breadcrumb-container {
        position: relative;
        padding: 15px !important;

        .breadcrumb-detail-container {
            padding: 0px !important;
        }

        .project-name-container {
            h3 {
                font-size: 14px;
            }
        }

        .menu-icon-button {
            img {
                width: 15px;
            }
        }

        .breadcrumb {
            li {
                font-size: 8px;
            }
        }
    }
    .user-profile-container {
        display: none !important;
    }
    .project-name-container {
        width: calc(100% - 87px);

        .project-name {
            width: 100%;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }
    }

    .modal {
        &.site-detail-modal {
            .modal-dialog {
                margin: 0 !important;
                position: absolute !important;
                right: 0 !important;
                width: 90%;
            }

            .modal-body {
                height: calc(100vh - 2px) !important;
                overflow-y: auto;

                &::-webkit-scrollbar {
                    width: 6px;
                    height: 3px;
                }

                &::-webkit-scrollbar-track {
                    border-radius: 10px;
                    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
                    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
                }

                &::-webkit-scrollbar-thumb {
                    border-radius: 10px;
                    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
                    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
                }
            }
        }
    }
}

/* ---------------------------------------------------
    MEDIAQUERIES
----------------------------------------------------- */


.color-picker-input {
    position: relative;

    input[type="color"] {
        position: absolute;
        right: 10px;
        margin-top: -11px;
        top: 50%;
    }
}

.content-body-active {
    max-width: calc(100% - 85px);
}

.custom-tab-container {
    padding: 0px;
}

.custom-tab-content-container {
    padding: 10px 0px;
}

.detail-page-label {
    background-color: #f2f3f8;
    font-weight: 400;
}

.ng-select.ng-select-single .ng-select-container {
    height: 46px;
    border-radius: 0px;
}
