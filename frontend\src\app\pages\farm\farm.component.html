<div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Farm Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'Farm.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('FARM','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('FARM','AddButton')">
            <span class="hidden-xs">{{'Farm.ADD_NEW_FARM' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Farm.name' | translate}}</th>
					    		<th>{{'Farm.country' | translate}}</th>
					    		<th>{{'Farm.latitude' | translate}}</th>
					    		<th>{{'Farm.longitude' | translate}}</th>
					    		<th>{{'Farm.description' | translate}}</th>
					    		<th>{{'Farm.propertyNo' | translate}}</th>
					    		<th>{{'Farm.farmingUrl' | translate}}</th>
					    		<th>{{'Farm.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        			<td>{{record.name}}</td>
					        			<td>{{record.country}}</td>
					        			<td>{{record.latitude}}</td>
					        			<td>{{record.longitude}}</td>
					        		<td>
					        			<div [innerHtml]="record.description" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        			<td>{{record.propertyNo}}</td>
					        		<td>
					        			<div [innerHtml]="record.farmingUrl" style="max-height: 100px;overflow:auto"></div>
					        		</td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">	         
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('FARM','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('FARM','DetailButton')"
				              (click)="loadDetailPage(record.id)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('FARM','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('FARM','EditButton')"
                           [routerLink]="['/dashboard/farm/edit/'+record.id]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('FARM','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('FARM','DeleteButton')"
	                           (click)="remove(record.id)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>		
            </table>
    </div>
</div>

<app-farm-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-farm-edit>

<div class="modal fade site-detail-modal right" id="farmDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="farmDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-farm-detail [recordId]="selectedId"></app-farm-detail>
            </div>
        </div>
    </div>
</div>

