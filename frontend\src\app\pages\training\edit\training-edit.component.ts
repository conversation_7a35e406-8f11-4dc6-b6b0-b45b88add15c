import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { Training } from '../../../models/training';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import {CommonUtil} from '../../../shared/common.util';
import { TrainingManager } from '../training.manager';

import { CategoryManager } from '../../category/category.manager';
import { Category } from '../../../models/category';
import { SubCategoryManager } from '../../subcategory/subcategory.manager';
import { SubCategory } from '../../../models/subcategory';
import { LanguageManager } from '../../language/language.manager';
import { Language } from '../../../models/language';
import { CommonEventService } from '../../../shared/common.event.service';
declare const $: any;

@Component({
  selector: 'app-training-edit',
  templateUrl: './training-edit.component.html',
  styleUrls: ['./training-edit.component.scss']
})

export class TrainingEditComponent extends BaseEditComponent implements OnInit {
  public training: Training;
	public categories:Category[];
	public subCategories:SubCategory[];
	public languages:Language[];
  
  constructor(protected route: ActivatedRoute, protected trainingManager: TrainingManager, 
  			  protected toastService: ToastService,protected loadingService: LoadingService, protected router: Router, 
  			  protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService 
  			  , private categoryManager:CategoryManager, private subcategoryManager:SubCategoryManager, private languageManager:LanguageManager
  			  ,public commonUtil:CommonUtil ) {
    	super(trainingManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
  	this.training = new Training();
  	this.training.isActive=true;   
    this.setRecord(this.training);
	
     
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
  	this.categories = new Array<Category>();
  	this.subCategories = new Array<SubCategory>();
  	this.languages = new Array<Language>();
    this.init();
  }

  onFetchCompleted() {
  	this.training = Training.fromResponse(this.record);
    this.setRecord(this.training);
  }

  
  
  async fetchAssociatedData() {
	this.categories = await this.categoryManager.fetchAllData(null);       		
	this.subCategories = await this.subcategoryManager.fetchAllData(null);       		
	this.languages = await this.languageManager.fetchAllData(null);       		
    this.afterFetchAssociatedCompleted();
  }
  
  afterFetchAssociatedCompleted() {
    	const categoryIdId: string = this.route.snapshot.queryParamMap.get('Category');
		if (categoryIdId){
			this.onAssociatedValueSelected({"id":categoryIdId},'trainingCategoryIdSelect');
		}
    	const subCategoryIdId: string = this.route.snapshot.queryParamMap.get('SubCategory');
		if (subCategoryIdId){
			this.onAssociatedValueSelected({"id":subCategoryIdId},'trainingSubCategoryIdSelect');
		}
    	const languageIdId: string = this.route.snapshot.queryParamMap.get('Language');
		if (languageIdId){
			this.onAssociatedValueSelected({"id":languageIdId},'trainingLanguageIdSelect');
		}
	}
  
  onSaveSuccess(data: any) {
  	this.navigate('/dashboard/training');
  }	
	  
	
	checkConditionToReload(records: BaseModel[], selectedRecord: any){
		if (!records.some(x => x.id === selectedRecord.id)) {
			this.fetchAssociatedData();
		}
	}
	
	onAssociatedValueSelected(selectedRecord: any, selectedField: any) {	
	if(this.request.popupId){
		$('#'+this.request.popupId).appendTo('body').modal('hide');
	}
		if((!this.isNullOrUndefined(selectedField) && selectedField==='trainingCategoryIdSelect') || this.request.popupId==='trainingCategoryIdPopup'){
			this.training.categoryId = selectedRecord.id;
			this.checkConditionToReload(this.categories, selectedRecord);
			return;
	    }
		if((!this.isNullOrUndefined(selectedField) && selectedField==='trainingSubCategoryIdSelect') || this.request.popupId==='trainingSubCategoryIdPopup'){
			this.training.subCategoryId = selectedRecord.id;
			this.checkConditionToReload(this.subCategories, selectedRecord);
			return;
	    }
		if((!this.isNullOrUndefined(selectedField) && selectedField==='trainingLanguageIdSelect') || this.request.popupId==='trainingLanguageIdPopup'){
			this.training.languageId = selectedRecord.id;
			this.checkConditionToReload(this.languages, selectedRecord);
			return;
	    }
  	
	 }
}
