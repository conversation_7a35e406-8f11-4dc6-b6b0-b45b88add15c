<div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">UserAssignTraining Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'UserAssignTraining.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('USERASSIGNTRAINING','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('USERASSIGNTRAINING','AddButton')">
            <span class="hidden-xs">{{'UserAssignTraining.ADD_NEW_USERASSIGNTRAINING' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Users.objName' | translate}} {{'Users.email' | translate}}</th>
					    		<th>{{'Training.objName' | translate}} {{'Training.videoTitle' | translate}}</th>
					    		<th>{{'UserAssignTraining.status' | translate}}</th>
					    		<th>{{'UserAssignTraining.userVideoUrl' | translate}}</th>
					    		<th>{{'UserAssignTraining.assignedDate' | translate}}</th>
					    		<th>{{'Farm.objName' | translate}} {{'Farm.name' | translate}}</th>
					    		<th>{{'UserAssignTraining.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        		<td>
				                    		<a *ngIf="record.userIdDetail" class="primary-color" [routerLink]="['/dashboard/users/detail/'+record.userIdDetail.id]">
				                       		{{record.userIdDetail.email}}
						        		
						        		</a> 
					        		</td>
					        		<td>
				                    		<a *ngIf="record.trainingIdDetail" class="primary-color" [routerLink]="['/dashboard/training/detail/'+record.trainingIdDetail.id]">
				                       		{{record.trainingIdDetail.videoTitle}}
						        		
						        		</a> 
					        		</td>
					        			<td>{{record.status}}</td>
					        		<td>
					        			<div [innerHtml]="record.userVideoUrl" style="max-height: 100px;overflow:auto"></div>
					        		</td>
				        			<td>{{record.assignedDate|date:'MM/dd/yyyy hh:mm'}} </td>
					        		<td>
				                    		<a *ngIf="record.farmIdDetail" class="primary-color" [routerLink]="['/dashboard/farm/detail/'+record.farmIdDetail.id]">
				                       		{{record.farmIdDetail.name}}
						        		
						        		</a> 
					        		</td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">	         
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('USERASSIGNTRAINING','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('USERASSIGNTRAINING','DetailButton')"
				              (click)="loadDetailPage(record.id)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('USERASSIGNTRAINING','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('USERASSIGNTRAINING','EditButton')"
                           [routerLink]="['/dashboard/user-assign-training/edit/'+record.id]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('USERASSIGNTRAINING','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('USERASSIGNTRAINING','DeleteButton')"
	                           (click)="remove(record.id)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>		
            </table>
    </div>
</div>

<app-userassigntraining-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-userassigntraining-edit>

<div class="modal fade site-detail-modal right" id="userAssignTrainingDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="userAssignTrainingDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-userassigntraining-detail [recordId]="selectedId"></app-userassigntraining-detail>
            </div>
        </div>
    </div>
</div>

