import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
import { Category } from './category';
import { Language } from './language';
export class SubCategory extends BaseModel {
	
				tenantId: number;
			  	slug: string;
			  	categoryIdDetail: Category;
			  	categoryId: string;
			  	title: string;
			  	description: string;
			  	languageIdDetail: Language;
			  	languageId: string;
			  	commonTitle: string;
			  	groupCode: string;
	
    constructor() {
        super();
			this.isDeleted=false;
			this.isActive=true;
    }
    
   static fromResponse(data: any) : SubCategory {
		const obj = new SubCategory();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
	  	obj.categoryIdDetail = data.categoryIdDetail;
	  	obj.categoryId = data.categoryId;
		obj.title = data.title;
		obj.description = data.description;
	  	obj.languageIdDetail = data.languageIdDetail;
	  	obj.languageId = data.languageId;
		obj.commonTitle = data.commonTitle;
		obj.groupCode = data.groupCode;
		return obj;
	}

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		if (this.isNullOrUndefinedAndEmpty(this.title)) {
            form.controls.title.setErrors({ invalid: true });
            return false;
        }
        return true;
    }

    forRequest() {
				this.title = this.trimMe(this.title);
				this.description = this.trimMe(this.description);
				this.commonTitle = this.trimMe(this.commonTitle);
				this.groupCode = this.trimMe(this.groupCode);
        return this;
    }
}
