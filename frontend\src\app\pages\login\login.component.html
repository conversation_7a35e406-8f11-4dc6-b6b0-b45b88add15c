<div id="user-login">
  <div class="container-fluid">
    <div class="row">
      <form autocomplete="off" novalidate="novalidate" #loginForm="ngForm">
        <div class="col-md-4 col-xs-12 col-sm-5 bg-color">
          <div class="row">
            <div class="col-md-12 col-xs-12 col-sm-12">
              <div class="inner-widget">
                <div class="logo padding-top-20 padding-bottom-20">
                  <img src="assets/images/logo/Icon-black.png" class="img-responsive"/>
                </div>
              </div>
            </div>
          </div>
          <div class="row" id="email-verification" *ngIf="view==='EMAILSLIDE'">
            <div class="col-md-12 text-center">
              <div class="inner-widget">
                <div class="page-heading-title padding-top-20 padding-bottom-20">
                  <h3>Login to your account</h3>
                </div>
              </div>
            </div>
            <div class="col-md-8 col-md-offset-2">
              <div class="form-group" [ngClass]="{'email-validation':!useEmail.valid && !onClickValidation}">
                <label class="control-label">
                  {{"LOGIN.EMAIL" | translate}}
                </label>
                <input type="email" class="form-control" placeholder="Enter Email"
                       required="required" name="useEmail" #useEmail="ngModel" autocomplete="nope"
                       [(ngModel)]="data.email" autofocus email
                       pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$"/>
              </div>
              <div class="form-group">
                <label class="control-label">
                  {{"LOGIN.PASSWORD" | translate}}
                </label>
                <input type="password" class="form-control"
                       name="userPassword" placeholder="Enter Password" #userPassword="ngModel"
                       [(ngModel)]="data.password"
                       required="required" [ngClass]="{'input-border':data.password!=null}"/>
              </div>
              <div class="text-right">
                <h5><a (click)="gotoForgotPassword()">Forgot Password?</a></h5>
              </div>
              <button class="btn btn-info btn-login"
                      (click)="login(loginForm.form)">
                Login
              </button>
            </div>
          </div>
          <div class="row" id="Forgot_Password" *ngIf="view==='FORGOTPASSWORD'">
            <div class="col-md-12 text-center">
              <div class="inner-widget">
                <div class="page-heading-title padding-top-20 padding-bottom-20">
                  <h3>Forgot Password</h3>
                </div>
              </div>
            </div>
            <div class="col-md-8 col-md-offset-2">
              <div class="form-group"
                   [ngClass]="{'email-validation':!useEmail.valid && !onClickValidation}">
                <label class="control-label">
                  {{"LOGIN.EMAIL" | translate}}
                </label>
                <input type="email" class="form-control" placeholder="Enter Email"
                       required="required" name="useEmail" #useEmail="ngModel" autocomplete="nope"
                       [(ngModel)]="data.email" autofocus email
                       pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$"/>
              </div>
              <div class="text-right">
                <h5><a (click)="goToLogin()">Back To Login?</a></h5>
              </div>
              <button class="btn btn-info btn-login"
                      (click)="resetPassword(loginForm.form)">SEND
              </button>
            </div>
          </div>
        </div>
        <div class="col-md-8 col-xs-12 col-sm-7">
          <div class="row">
            <div class="col-md-12 col-xs-12 col-sm-12">
              <div class="inner-widget display-align">
                <div class="img">
                  <img src="assets/images/banner/logo.svg" class="img-responsive"/>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="inner-widget display-align">
              <div class=" banner-footer">
                <img src="assets/images/banner/login-footer.png" class="img-responsive">
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
