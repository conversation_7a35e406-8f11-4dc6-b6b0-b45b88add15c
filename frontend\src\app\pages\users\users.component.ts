import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseListComponent } from 'src/app/config/base.list.component';
import { Users } from '../../models/users';
import { LoadingService } from '../../services/loading.service';
import { AuthService } from '../../shared/auth.services';
import { CommonService } from '../../shared/common.service';
import { CommonUtil } from '../../shared/common.util';
import { ToastService } from '../../shared/toast.service';
import { UsersManager } from './users.manager';

declare const $: any;

@Component({
    selector: 'app-users',
    templateUrl: './users.component.html',
    styleUrls: ['./users.component.scss']
})
export class UsersComponent extends BaseListComponent implements OnInit, OnDestroy {

    constructor(protected usersManager: UsersManager, protected toastService: ToastService, public authService: AuthService,
        protected loadingService: LoadingService, protected commonService: CommonService, protected router: Router,
        public commonUtil: CommonUtil) {
        super(usersManager, commonService, toastService, loadingService, router);
    }

    ngOnInit() {
        this.records = [] as Users[];
        this.isPlusButton = !this.isNullOrUndefined(this.onAssociatedValueSelected);
        this.init();
    }

    onFetchCompleted() {
        this.records = this.records.sort((a: any, b: any) => {
            if (a.fullName < b.fullName) {
                return -1;
            }
            if (a.fullName > b.fullName) {
                return 1;
            }
            return 0;
        });
        super.onFetchCompleted();
    }

    onItemSelection(record: Users) {
        this.onAssociatedValueSelected(record);
    }

    onCancel() {
        if (!this.isNullOrUndefined(this.dtElement.dtInstance)) {
            this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
                dtInstance.destroy();
            });
        }
        this.init();
    }

    removeSuccess() {
        this.onCancel();
    }

    ngOnDestroy() {
        this.clean();
    }
}
