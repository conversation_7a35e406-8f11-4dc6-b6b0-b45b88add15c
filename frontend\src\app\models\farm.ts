import { BaseModel } from '../config/base.model';
import { ToastService } from '../shared/toast.service';
import { ValidationService } from '../shared/validation.service';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';
export class Farm extends BaseModel {
	
				tenantId: number;
			  	slug: string;
			  	name: string;
			  	country: string;
			  	latitude: string;
			  	longitude: string;
			  	description: string;
			  	propertyNo: string;
			  	farmingUrl: string;
	
    constructor() {
        super();
			this.isDeleted=false;
			this.isActive=true;
    }
    
   static fromResponse(data: any) : Farm {
		const obj = new Farm();
		obj.id = data.id;
		obj.tenantId = data.tenantId;
		obj.slug = data.slug;
		obj.createdBy = data.createdBy;
		obj.updatedBy = data.updatedBy;
		obj.createdOn = data.createdOn;
		obj.updatedOn = data.updatedOn;
		obj.isDeleted = data.isDeleted;
		obj.isActive = data.isActive;
		obj.name = data.name;
		obj.country = data.country;
		obj.latitude = data.latitude;
		obj.longitude = data.longitude;
		obj.description = data.description;
		obj.propertyNo = data.propertyNo;
		obj.farmingUrl = data.farmingUrl;
		return obj;
	}

    isValidateRequest(form: any, toastService: ToastService, translate: TranslateService) {
		if (this.isNullOrUndefinedAndEmpty(this.name)) {
            form.controls.name.setErrors({ invalid: true });
            return false;
        }
        return true;
    }

    forRequest() {
				this.name = this.trimMe(this.name);
				this.country = this.trimMe(this.country);
				this.latitude = this.trimMe(this.latitude);
				this.longitude = this.trimMe(this.longitude);
				this.description = this.trimMe(this.description);
				this.propertyNo = this.trimMe(this.propertyNo);
				this.farmingUrl = this.trimMe(this.farmingUrl);
        return this;
    }
}
