import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { SubCategory } from '../../../models/subcategory';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import {CommonUtil} from '../../../shared/common.util';
import { SubCategoryManager } from '../subcategory.manager';

import { CategoryManager } from '../../category/category.manager';
import { Category } from '../../../models/category';
import { LanguageManager } from '../../language/language.manager';
import { Language } from '../../../models/language';
import { CommonEventService } from '../../../shared/common.event.service';
declare const $: any;

@Component({
  selector: 'app-subcategory-edit',
  templateUrl: './subcategory-edit.component.html',
  styleUrls: ['./subcategory-edit.component.scss']
})

export class SubCategoryEditComponent extends BaseEditComponent implements OnInit {
  public subCategory: SubCategory;
	public categories:Category[];
	public languages:Language[];
  
  constructor(protected route: ActivatedRoute, protected subcategoryManager: SubCategoryManager, 
  			  protected toastService: ToastService,protected loadingService: LoadingService, protected router: Router, 
  			  protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService 
  			  , private categoryManager:CategoryManager, private languageManager:LanguageManager
  			  ,public commonUtil:CommonUtil ) {
    	super(subcategoryManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
  	this.subCategory = new SubCategory();
  	this.subCategory.isActive=true;   
    this.setRecord(this.subCategory);
	
     
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
  	this.categories = new Array<Category>();
  	this.languages = new Array<Language>();
    this.init();
  }

  onFetchCompleted() {
  	this.subCategory = SubCategory.fromResponse(this.record);
    this.setRecord(this.subCategory);
  }

  
  
  async fetchAssociatedData() {
	this.categories = await this.categoryManager.fetchAllData(null);       		
	this.languages = await this.languageManager.fetchAllData(null);       		
    this.afterFetchAssociatedCompleted();
  }
  
  afterFetchAssociatedCompleted() {
    	const categoryIdId: string = this.route.snapshot.queryParamMap.get('Category');
		if (categoryIdId){
			this.onAssociatedValueSelected({"id":categoryIdId},'subCategoryCategoryIdSelect');
		}
    	const languageIdId: string = this.route.snapshot.queryParamMap.get('Language');
		if (languageIdId){
			this.onAssociatedValueSelected({"id":languageIdId},'subCategoryLanguageIdSelect');
		}
	}
  
  onSaveSuccess(data: any) {
  	this.navigate('/dashboard/sub-category');
  }	
	  
	
	checkConditionToReload(records: BaseModel[], selectedRecord: any){
		if (!records.some(x => x.id === selectedRecord.id)) {
			this.fetchAssociatedData();
		}
	}
	
	onAssociatedValueSelected(selectedRecord: any, selectedField: any) {	
	if(this.request.popupId){
		$('#'+this.request.popupId).appendTo('body').modal('hide');
	}
		if((!this.isNullOrUndefined(selectedField) && selectedField==='subCategoryCategoryIdSelect') || this.request.popupId==='subCategoryCategoryIdPopup'){
			this.subCategory.categoryId = selectedRecord.id;
			this.checkConditionToReload(this.categories, selectedRecord);
			return;
	    }
		if((!this.isNullOrUndefined(selectedField) && selectedField==='subCategoryLanguageIdSelect') || this.request.popupId==='subCategoryLanguageIdPopup'){
			this.subCategory.languageId = selectedRecord.id;
			this.checkConditionToReload(this.languages, selectedRecord);
			return;
	    }
  	
	 }
}
