<div class="breadcrumb-container" *ngIf="!isPlusButton && !isDetailPage">
    <div class="col-md-12 breadcrumb-detail-container">
        <a class="menu-icon-button" (click)="commonUtil.toggleMenu()">
            <img src="/assets/images/menu.png" class="img-responsive">
        </a>
        <div class="project-name-container">
            <h3 class="project-name">Moment Administration</h3>
            <ol class="breadcrumb">
                <li><a [routerLink]="['/dashboard']">{{'DASHBOARD.objName' | translate}}</a></li>
                <li class="active">{{'Moment.objName' | translate}}</li>
            </ol>
        </div>
        <button class="btn btn-primary add-new-item-button pull-right" (click)="onNewRecord()"
                *ngIf="authService.isAccessible('MOMENT','AddButton')"
                title="{{'COMMON.ADD' | translate}}"
                [class.disabled]="authService.isDisabled('MOMENT','AddButton')">
            <span class="hidden-xs">{{'Moment.ADD_NEW_MOMENT' | translate}}</span>
            <span class="visible-xs">
                <i class="fa fa-plus-square-o" aria-hidden="true"></i>
            </span>
        </button>
    </div>
    <div class="clearfix"></div>
</div>
<div class="clearfix"></div>
<div class="site-page-container" [hidden]="request.loadEditPage" [ngClass]="{'no-padding':isDetailPage}">
    <div class="site-card" *ngIf="hasDataLoad">
            <table class="table table-bordered table-striped" datatable [dtOptions]="dtOptions" [dtTrigger]="dtTrigger">
            	<thead>
			      <tr>
					    		<th>{{'Farm.objName' | translate}} {{'Farm.name' | translate}}</th>
					    		<th>{{'Moment.type' | translate}}</th>
					    		<th>{{'Moment.description' | translate}}</th>
					    		<th>{{'Moment.mediaType' | translate}}</th>
					    		<th>{{'Moment.mediaUrl' | translate}}</th>
					    		<th>{{'UserAssignTraining.objName' | translate}} {{'UserAssignTraining.id' | translate}}</th>
					    		<th>{{'Moment.userVideo' | translate}}</th>
					    		<th>{{'Moment.instruction' | translate}}</th>
					    		<th>{{'Moment.completedBy' | translate}}</th>
					    		<th>{{'Moment.status' | translate}}</th>
					    		<th>{{'Moment.createdOn' | translate}}</th>
			        <th width="50">{{'COMMON.ACTION' | translate}}</th>
			      </tr>
			    </thead>
			    <tbody>
				    <tr *ngFor="let record of records">
					        		<td>
				                    		<a *ngIf="record.farmIdDetail" class="primary-color" [routerLink]="['/dashboard/farm/detail/'+record.farmIdDetail.id]">
				                       		{{record.farmIdDetail.name}}
						        		
						        		</a> 
					        		</td>
					        			<td>{{record.type}}</td>
					        		<td>
					        			<div [innerHtml]="record.description" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        			<td>{{record.mediaType}}</td>
					        		<td>
					        			<div [innerHtml]="record.mediaUrl" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        		<td>
				                    		<a *ngIf="record.assignTrainingIdDetail" class="primary-color" [routerLink]="['/dashboard/userassigntraining/detail/'+record.assignTrainingIdDetail.id]">
				                       		{{record.assignTrainingIdDetail.id}}
						        		
						        		</a> 
					        		</td>
					        		<td>
					        			<div [innerHtml]="record.userVideo" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        		<td>
					        			<div [innerHtml]="record.instruction" style="max-height: 100px;overflow:auto"></div>
					        		</td>
					        			<td>{{record.completedBy}}</td>
					        			<td>{{record.status}}</td>
				        			<td>{{record.createdOn|date:'MM/dd/yyyy hh:mm'}} </td>
				        <td class="text-center">	         
				          <a title="Select" class="btn btn-info btn-xs margin-right-5" (click)="onItemSelection(record)"
				            *ngIf="isPlusButton">
				            {{'COMMON.SELECT' | translate}}
				          </a>
				          <a title="Detail" class="action-button" *ngIf="authService.isAccessible('MOMENT','DetailButton') && !isDetailPage && !isPlusButton"
							[class.disabled]="authService.isDisabled('MOMENT','DetailButton')"
				              (click)="loadDetailPage(record.id)">
				              <i class="fa fa-info-circle" aria-hidden="true"></i>
				            </a>
				          <a title="Edit" class="action-button"
                           *ngIf="authService.isAccessible('MOMENT','EditButton') && !isDetailPage && !isPlusButton"
                           [class.disabled]="authService.isDisabled('MOMENT','EditButton')"
                           [routerLink]="['/dashboard/moment/edit/'+record.id]">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
	                      </a>
	                      <a title="Delete" class="action-button"
	                           *ngIf="authService.isAccessible('MOMENT','DeleteButton') && !isPlusButton"
	                           [class.disabled]="authService.isDisabled('MOMENT','DeleteButton')"
	                           (click)="remove(record.id)">
	                            <i class="fa fa-trash" aria-hidden="true"></i>
	                      </a>
				        </td>
				      </tr>
				 </tbody>		
            </table>
    </div>
</div>

<app-moment-edit *ngIf="request.loadEditPage" [onCancel]="onCancel.bind(this)"></app-moment-edit>

<div class="modal fade site-detail-modal right" id="momentDetailPage" tabindex="-1" role="dialog"
     aria-labelledby="momentDetailPage" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body" *ngIf="selectedId">
                <app-moment-detail [recordId]="selectedId"></app-moment-detail>
            </div>
        </div>
    </div>
</div>

