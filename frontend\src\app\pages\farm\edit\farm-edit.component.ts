import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { BaseEditComponent } from '../../../config/base.edit.component';
import { BaseModel } from '../../../config/base.model';
import { Farm } from '../../../models/farm';
import { LoadingService } from '../../../services/loading.service';
import { CommonService } from '../../../shared/common.service';
import { ToastService } from '../../../shared/toast.service';
import { AuthService } from '../../../shared/auth.services';
import {CommonUtil} from '../../../shared/common.util';
import { FarmManager } from '../farm.manager';

import { CommonEventService } from '../../../shared/common.event.service';
declare const $: any;

@Component({
  selector: 'app-farm-edit',
  templateUrl: './farm-edit.component.html',
  styleUrls: ['./farm-edit.component.scss']
})

export class FarmEditComponent extends BaseEditComponent implements OnInit {
  public farm: Farm;
  
  constructor(protected route: ActivatedRoute, protected farmManager: FarmManager, 
  			  protected toastService: ToastService,protected loadingService: LoadingService, protected router: Router, 
  			  protected commonService: CommonService, public authService: AuthService, protected translateService: TranslateService 
  			  
  			  ,public commonUtil:CommonUtil ) {
    	super(farmManager, commonService, toastService, loadingService, route, router, translateService);
  }

  ngOnInit() {
  	this.farm = new Farm();
  	this.farm.isActive=true;   
    this.setRecord(this.farm);
	
     
    this.isPlusButton = !this.isNullOrUndefined(this.onCancel);
    this.init();
  }

  onFetchCompleted() {
  	this.farm = Farm.fromResponse(this.record);
    this.setRecord(this.farm);
  }

  
  
  
  afterFetchAssociatedCompleted() {
	}
  
  onSaveSuccess(data: any) {
  	this.navigate('/dashboard/farm');
  }	
	  
	
}
